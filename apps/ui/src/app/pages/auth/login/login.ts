import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  Auth,
  GoogleAuthProvider,
  signInWithEmailAndPassword,
  signInWithPopup,
} from '@angular/fire/auth';
import { Router, RouterLink } from '@angular/router';
import { toastConfig$ } from '../../../store/signals';
import { PendingRegistrationService } from '../../../services/pending-registration.service';

@Component({
  selector: 'app-login',
  imports: [ReactiveFormsModule, CommonModule, RouterLink],
  templateUrl: './login.html',
})
export class Login {
  private auth: Auth = inject(Auth);
  private router: Router = inject(Router);
  private pendingRegistrationService: PendingRegistrationService = inject(
    PendingRegistrationService
  );

  loginForm = new FormGroup({
    email: new FormControl('', [Validators.required]),
    password: new FormControl('', [Validators.required]),
  });

  async login() {
    if (this.loginForm.valid) {
      const data: any = this.loginForm.value;
      try {
        const response = await signInWithEmailAndPassword(
          this.auth,
          data.email,
          data.password
        );
        console.log('Successfully Logged:', response);
        await this.router.navigate(['/dashboard']);
      } catch (e) {
        if (e.code === 'auth/invalid-credential') {
          console.error('Invalid email or password.');
          toastConfig$.set({ status: 'error', message: e.code });
        } else {
          console.error('Error signing in: ', e);
          toastConfig$.set({
            status: 'error',
            message: e.code ?? 'Unable to login',
          });
        }
      }
    } else {
      console.log('Form is invalid');
      toastConfig$.set({ status: 'error', message: 'All fields required' });
    }
  }

  async loginWithGoogle() {
    try {
      const response = await signInWithPopup(
        this.auth,
        new GoogleAuthProvider()
      );
      console.log('Successfully Logged:', response);

      // Check user registration status after Google login
      await this.handlePostLoginNavigation(response.user);
    } catch (e) {
      console.error('Error signing in: ', e);
      toastConfig$.set({
        status: 'error',
        message: e.code ?? 'Unable to login',
      });
    }
  }

  /**
   * Handles navigation after successful login based on user registration status
   * Ensures Google users follow the same approval workflow as email users
   */
  private async handlePostLoginNavigation(user: any) {
    try {
      // Get user's current claims to check approval status
      const claims = await this.pendingRegistrationService.getCurrentUserClaims();

      if (!claims || !claims.status) {
        // New user (no registration record) - redirect to complete registration
        console.log('New Google user detected, redirecting to registration');
        toastConfig$.set({
          status: 'info',
          message: 'Please complete your registration to continue',
        });
        await this.router.navigate(['/auth/register'], {
          queryParams: {
            provider: 'google',
            email: user.email,
            name: user.displayName
          }
        });
      } else if (claims.status === 'pending') {
        // User exists but pending approval
        console.log('Google user pending approval, redirecting to pending page');
        await this.router.navigate(['/auth/pending-approval']);
      } else if (claims.status === 'approved' && claims.role) {
        // Approved user - allow dashboard access
        console.log('Approved Google user, redirecting to dashboard');
        toastConfig$.set({
          status: 'success',
          message: 'Welcome back!',
        });
        await this.router.navigate(['/dashboard']);
      } else if (claims.status === 'rejected') {
        // Rejected user - redirect to pending page (will show rejection info)
        console.log('Rejected Google user, redirecting to pending page');
        await this.router.navigate(['/auth/pending-approval']);
      } else {
        // Unknown status - redirect to pending page for safety
        console.warn('Unknown user status:', claims.status);
        await this.router.navigate(['/auth/pending-approval']);
      }
    } catch (error) {
      console.error('Error checking user registration status:', error);

      // On error, try to check if user has any registration record
      try {
        const registrationStatus = await this.pendingRegistrationService
          .getCurrentUserRegistrationStatus();

        if (registrationStatus.status === 'not_found') {
          // No registration found - redirect to register
          await this.router.navigate(['/auth/register'], {
            queryParams: {
              provider: 'google',
              email: user.email,
              name: user.displayName
            }
          });
        } else {
          // Has registration - redirect to pending approval
          await this.router.navigate(['/auth/pending-approval']);
        }
      } catch (fallbackError) {
        console.error('Fallback registration check failed:', fallbackError);
        // Ultimate fallback - redirect to register
        await this.router.navigate(['/auth/register'], {
          queryParams: {
            provider: 'google',
            email: user.email,
            name: user.displayName
          }
        });
      }
    }
  }
}
