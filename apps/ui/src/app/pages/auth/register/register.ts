import { CommonModule } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  Auth,
  createUserWithEmailAndPassword,
  updateProfile,
} from '@angular/fire/auth';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { toastConfig$ } from '../../../store/signals';
import { PendingRegistrationService } from '../../../services/pending-registration.service';

@Component({
  selector: 'app-register',
  imports: [ReactiveFormsModule, CommonModule, RouterLink],
  templateUrl: './register.html',
})
export class Register implements OnInit {
  private auth: Auth = inject(Auth);
  private router: Router = inject(Router);
  private route: ActivatedRoute = inject(ActivatedRoute);
  private pendingRegistrationService: PendingRegistrationService = inject(
    PendingRegistrationService
  );

  // Track if this is a Google user registration
  isGoogleUser = false;
  googleUserData: { email: string; name: string } | null = null;

  // Form for user registration with validation
  registerForm = new FormGroup({
    name: new FormControl('', [Validators.required, Validators.minLength(2)]),
    email: new FormControl('', [Validators.required, Validators.email]),
    password: new FormControl('', [
      Validators.required,
      Validators.minLength(6),
    ]),
    role: new FormControl('', [Validators.required]),
  });
  // Loading state to prevent multiple submissions
  isRegistering = false;

  async ngOnInit() {
    // Check if user came from Google login
    const provider = this.route.snapshot.queryParams['provider'];
    const email = this.route.snapshot.queryParams['email'];
    const name = this.route.snapshot.queryParams['name'];

    if (provider === 'google' && email && name) {
      this.isGoogleUser = true;
      this.googleUserData = { email, name };

      // Pre-populate form for Google users
      this.registerForm.patchValue({
        email: email,
        name: name
      });

      // Make email and password fields readonly/optional for Google users
      this.registerForm.get('email')?.disable();
      this.registerForm.get('password')?.clearValidators();
      this.registerForm.get('password')?.updateValueAndValidity();

      console.log('Google user detected, form pre-populated');
    }
  }

  /**
   * Handles user registration with the new pending approval system
   * Creates Firebase Auth user and submits registration for admin approval
   */
  async register() {
    if (this.registerForm.valid && !this.isRegistering) {
      this.isRegistering = true;
      const data: any = this.registerForm.value;

      try {
        let userCredential: any;
        let userData: any;

        if (this.isGoogleUser && this.googleUserData) {
          // Google user - use existing authenticated user
          const currentUser = this.auth.currentUser;
          if (!currentUser) {
            throw new Error('Google user not authenticated');
          }

          userCredential = { user: currentUser };
          userData = {
            uid: currentUser.uid,
            email: this.googleUserData.email,
            name: this.googleUserData.name,
            role: data.role,
            provider: 'google'
          };

          console.log('Processing Google user registration:', userData);
        } else {
          // Email/password user - create new Firebase Auth user
          userCredential = await createUserWithEmailAndPassword(
            this.auth,
            data.email,
            data.password
          );

          console.log('Firebase Auth user created:', userCredential.user.uid);

          // Update user profile with display name
          await updateProfile(userCredential.user, { displayName: data.name });

          userData = {
            uid: userCredential.user.uid,
            email: data.email,
            name: data.name,
            role: data.role,
            provider: 'email'
          };
        }

        // Create pending registration (same for both Google and email users)
        const registrationResult =
          await this.pendingRegistrationService.createPendingRegistration({
            uid: userData.uid,
            email: userData.email,
            name: userData.name,
            role: userData.role,
            verificationData: this.getVerificationData(userData.role, userData.provider),
          });

        console.log('Pending registration created:', registrationResult);

        // Show success message and redirect to pending approval page
        const message = this.isGoogleUser
          ? 'Google registration submitted successfully! Please wait for admin approval.'
          : 'Registration submitted successfully! Please wait for admin approval.';

        toastConfig$.set({
          status: 'success',
          message: message,
        });

        // Redirect to pending approval page
        await this.router.navigate(['/auth/pending-approval']);
      } catch (e: any) {
        console.error('Error during registration:', e);

        // Handle specific Firebase Auth errors
        let errorMessage = this.isGoogleUser
          ? 'Google registration failed. Please try again.'
          : 'Registration failed. Please try again.';

        if (e.code === 'auth/email-already-in-use') {
          errorMessage = 'An account with this email already exists.';
        } else if (e.code === 'auth/weak-password') {
          errorMessage = 'Password is too weak. Please use at least 6 characters.';
        } else if (e.code === 'auth/invalid-email') {
          errorMessage = 'Please enter a valid email address.';
        } else if (e.message) {
          errorMessage = e.message;
        }

        toastConfig$.set({ status: 'error', message: errorMessage });
      } finally {
        this.isRegistering = false;
      }
    } else if (!this.registerForm.valid) {
      console.error('Registration form is invalid');
      toastConfig$.set({
        status: 'error',
        message: 'Please fill in all required fields correctly.',
      });
    }
  }

  /**
   * Generates role-specific verification data
   * This can be expanded to include additional verification requirements
   *
   * @param role - The selected user role
   * @param provider - The authentication provider ('email' or 'google')
   * @returns Verification data object
   */
  private getVerificationData(role: string, provider: string = 'email'): any {
    const verificationData: any = {
      provider: provider,
      registrationMethod: provider === 'google' ? 'Google OAuth' : 'Email/Password'
    };

    switch (role) {
      case 'student':
        // Students might need to provide student ID in the future
        verificationData.requiresStudentId = true;
        break;

      case 'trainer':
        // Trainers need credential verification
        verificationData.requiresCredentials = true;
        verificationData.requiresExperienceVerification = true;
        break;

      case 'college-staff':
        // College staff need institutional email verification
        verificationData.requiresInstitutionalEmail = true;
        verificationData.requiresDepartmentVerification = true;
        break;

      case 'parent':
        // Parents need to be linked to a student
        verificationData.requiresStudentLinkage = true;
        break;
    }

    return verificationData;
  }
}
