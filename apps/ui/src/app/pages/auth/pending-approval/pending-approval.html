<!-- Pending Approval Page -->
<div class="flex justify-center items-start pt-10 min-h-screen bg-base-300">
  <div class="card w-full max-w-2xl bg-base-100 shadow-xl">
    <div class="card-body">

      <!-- Loading State -->
      @if (isLoading()) {
        <div class="flex justify-center items-center py-12">
          <div class="loading loading-spinner loading-lg"></div>
          <span class="ml-4 text-lg">Checking registration status...</span>
        </div>
      } @else {

        <!-- Header -->
        <div class="text-center mb-6">
          <h2 class="card-title justify-center text-2xl mb-2">Registration Status</h2>
          <p class="text-base-content/70">Welcome to Worthy Freshers</p>
        </div>

        <!-- Status Alert -->
        @if (registrationStatus()) {
          <div class="alert mb-6" [class]="getStatusClass()">
            <!-- Status Icon -->
            @switch (registrationStatus()?.status) {
              @case ('pending') {
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none"
                     viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              }
              @case ('approved') {
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none"
                     viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              }
              @case ('rejected') {
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none"
                     viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              }
              @default {
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none"
                     viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              }
            }

            <div>
              <h3 class="font-bold">
                @switch (registrationStatus()?.status) {
                  @case ('pending') {
                    Registration Pending
                  }
                  @case ('approved') {
                    Registration Approved
                  }
                  @case ('rejected') {
                    Registration Rejected
                  }
                  @default {
                    Registration Status
                  }
                }
              </h3>
              <div class="text-sm">{{ getStatusMessage() }}</div>
            </div>
          </div>
        }

        <!-- Registration Details -->
        @if (registrationStatus()?.registrationData) {
          <div class="bg-base-200 rounded-lg p-4 mb-6">
            <h3 class="font-semibold mb-3">Registration Details</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
              <div>
                <span class="font-medium">Name:</span>
                <span class="ml-2">{{ registrationStatus()?.registrationData?.name }}</span>
              </div>
              <div>
                <span class="font-medium">Email:</span>
                <span class="ml-2">{{ registrationStatus()?.registrationData?.email }}</span>
              </div>
              <div>
                <span class="font-medium">Role:</span>
                <span class="ml-2">{{ getRoleDisplayName(registrationStatus()?.registrationData?.role || '') }}</span>
              </div>
              <div>
                <span class="font-medium">Submitted:</span>
                <span class="ml-2">{{ formatDate(registrationStatus()?.registrationData?.submittedAt) }}</span>
              </div>

              <!-- Show approval/rejection details if available -->
              @if (registrationStatus()?.registrationData?.approvedAt) {
                <div class="md:col-span-2">
                  <span class="font-medium">Approved:</span>
                  <span class="ml-2">{{ formatDate(registrationStatus()?.registrationData?.approvedAt) }}</span>
                </div>
              }

              @if (registrationStatus()?.registrationData?.rejectedAt) {
                <div class="md:col-span-2">
                  <span class="font-medium">Rejected:</span>
                  <span class="ml-2">{{ formatDate(registrationStatus()?.registrationData?.rejectedAt) }}</span>
                </div>
              }
            </div>
          </div>
        }

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-3 justify-center">

          <!-- Refresh Status Button -->
          <button
            class="btn btn-outline btn-primary"
            [class.loading]="isRefreshing()"
            [disabled]="isRefreshing()"
            (click)="onRefreshClick()">
            @if (!isRefreshing()) {
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24"
                   stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh Status
            } @else {
              Refreshing...
            }
          </button>

          <!-- Logout Button -->
          <button
            class="btn btn-ghost"
            (click)="logout()">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24"
                 stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
            Logout
          </button>
        </div>

        <!-- Help Information -->
        <div class="divider">Need Help?</div>

        <div class="text-center text-sm text-base-content/70">
          @if (registrationStatus()?.status === 'pending') {
            <p class="mb-2">
              Your registration is being reviewed by our administrators.
              This process typically takes 1-2 business days.
            </p>
            <p>
              You will receive an email notification once your registration is processed.
            </p>
          } @else if (registrationStatus()?.status === 'rejected') {
            <p class="mb-2">
              If you believe this rejection was made in error, please contact our support team.
            </p>
            <p>
              You may also submit a new registration with corrected information.
            </p>
            <div class="mt-4">
              <a class="btn btn-sm btn-outline" routerLink="/auth/register">
                Submit New Registration
              </a>
            </div>
          }

          <div class="mt-4">
            <p>
              For questions or support, please contact:
              <a href="mailto:<EMAIL>" class="link link-primary">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>

      }
    </div>
  </div>
</div>

<!-- Auto-refresh indicator -->
@if (!isLoading() && registrationStatus()?.status === 'pending') {
  <div class="fixed bottom-4 right-4">
    <div class="tooltip tooltip-left" data-tip="Status auto-refreshes every 30 seconds">
      <div class="badge badge-info gap-2">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        Auto-refresh
      </div>
    </div>
  </div>
}
