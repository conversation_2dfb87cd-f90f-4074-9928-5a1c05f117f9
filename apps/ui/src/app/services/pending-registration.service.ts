import { inject, Injectable } from '@angular/core';
import { Functions, httpsCallable } from '@angular/fire/functions';
import { Auth } from '@angular/fire/auth';
import { collection, collectionData, Firestore, onSnapshot, orderBy, query, where } from '@angular/fire/firestore';
import { BehaviorSubject, Observable } from 'rxjs';
import { HttpClient, HttpHeaders } from '@angular/common/http';

/**
 * Interface for pending registration data
 */
export interface PendingRegistration {
  uid: string;
  email: string;
  name: string;
  role: 'student' | 'trainer' | 'parent' | 'college-staff' | 'admin';
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: any; // Firestore Timestamp
  verificationData?: {
    [key: string]: any; // Allow dynamic properties
    studentId?: string;
    institutionEmail?: string;
    credentials?: string[];
    parentStudentLink?: string;
    requiresCredentials?: boolean;
    requiresInstitutionalEmail?: boolean;
    requiresStudentLinkage?: boolean;
    requiresExperienceVerification?: boolean;
    requiresDepartmentVerification?: boolean;
    requiresStudentId?: boolean;
  };
  approvedBy?: string;
  approvedAt?: any; // Firestore Timestamp
  rejectedBy?: string;
  rejectedAt?: any; // Firestore Timestamp
  rejectionReason?: string;
}

/**
 * Interface for registration status response
 */
export interface RegistrationStatus {
  status: 'pending' | 'approved' | 'rejected' | 'not_found';
  message?: string;
  registrationData?: {
    name: string;
    email: string;
    role: string;
    submittedAt: any;
    approvedAt?: any;
    rejectedAt?: any;
    rejectionReason?: string;
  };
  currentClaims?: any;
}

/**
 * Service for managing pending registrations
 * Handles creation, approval, rejection, and status checking of user registrations
 */
@Injectable({
  providedIn: 'root',
})
export class PendingRegistrationService {
  private functions: Functions = inject(Functions);
  private auth: Auth = inject(Auth);
  private firestore: Firestore = inject(Firestore);
  private http: HttpClient = inject(HttpClient);

  // Firebase project configuration - update these with your project details
  private readonly FIREBASE_PROJECT_ID = 'worthy-freshers'; // Replace with your project ID
  private readonly FIREBASE_REGION = 'us-central1';

  // Observable for pending registrations using collectionData
  public pendingRegistrations$: Observable<PendingRegistration[]>;

  // BehaviorSubject to track current user's registration status
  private userRegistrationStatusSubject =
    new BehaviorSubject<RegistrationStatus | null>(null);
  public userRegistrationStatus$ =
    this.userRegistrationStatusSubject.asObservable();

  constructor() {
    // Initialize pending registrations observable using collectionData
    this.initializePendingRegistrationsObservable();
  }

  /**
   * Creates a pending registration for a new user
   * This replaces the immediate role assignment in the original registration flow
   * Includes fallback to HTTP version if onCall version fails due to CORS
   *
   * @param userData - User registration data
   * @returns Promise with registration result
   */
  async createPendingRegistration(userData: {
    uid: string;
    email: string;
    name: string;
    role: string;
    verificationData?: any;
  }): Promise<any> {
    try {
      // First, try the onCall version (preferred)
      console.log(
        'Attempting to create pending registration via onCall function...'
      );
      const createPendingRegistration = httpsCallable(
        this.functions,
        'createPendingRegistration'
      );
      const result = await createPendingRegistration(userData);

      console.log('Pending registration created via onCall:', result.data);
      return result.data;
    } catch (error: any) {
      console.warn('onCall function failed, trying HTTP fallback:', error);

      // If onCall fails (possibly due to CORS), try HTTP version
      try {
        return await this.createPendingRegistrationHttp(userData);
      } catch (httpError) {
        console.error('Both onCall and HTTP methods failed:', {
          onCallError: error,
          httpError,
        });
        throw error; // Throw the original error
      }
    }
  }

  /**
   * Approves a pending registration (Admin only)
   * Grants the user access with their requested role
   * Uses HTTP as primary method with onCall fallback
   *
   * @param registrationId - UID of the user to approve
   * @returns Promise with approval result
   */
  async approvePendingRegistration(registrationId: string): Promise<any> {
    try {
      // Use HTTP version as primary method
      console.log('Attempting to approve registration via HTTP...');
      const result = await this.approvePendingRegistrationHttp(registrationId);
      console.log('Registration approved via HTTP:', result);
      return result;
    } catch (error: any) {
      console.error('Error approving registration via HTTP:', error);

      // Fallback to onCall method
      console.log('HTTP method failed, trying onCall fallback...');
      try {
        const approvePendingRegistration = httpsCallable(
          this.functions,
          'approvePendingRegistration'
        );
        const result = await approvePendingRegistration({ registrationId });

        console.log('Registration approved via onCall fallback:', result.data);
        return result.data;
      } catch (onCallError) {
        console.error('Both HTTP and onCall methods failed:', {
          httpError: error,
          onCallError,
        });
        throw onCallError;
      }
    }
  }

  /**
   * Rejects a pending registration (Admin only)
   * Denies access and provides rejection reason
   * Uses HTTP as primary method with onCall fallback
   *
   * @param registrationId - UID of the user to reject
   * @param reason - Reason for rejection
   * @returns Promise with rejection result
   */
  async rejectPendingRegistration(
    registrationId: string,
    reason: string
  ): Promise<any> {
    try {
      // Use HTTP version as primary method
      console.log('Attempting to reject registration via HTTP...');
      const result = await this.rejectPendingRegistrationHttp(
        registrationId,
        reason
      );
      console.log('Registration rejected via HTTP:', result);
      return result;
    } catch (error: any) {
      console.error('Error rejecting registration via HTTP:', error);

      // Fallback to onCall method
      console.log('HTTP method failed, trying onCall fallback...');
      try {
        const rejectPendingRegistration = httpsCallable(
          this.functions,
          'rejectPendingRegistration'
        );
        const result = await rejectPendingRegistration({
          registrationId,
          reason,
        });

        console.log('Registration rejected via onCall fallback:', result.data);
        return result.data;
      } catch (onCallError) {
        console.error('Both HTTP and onCall methods failed:', {
          httpError: error,
          onCallError,
        });
        throw onCallError;
      }
    }
  }

  /**
   * Gets current user's custom claims (role, status, etc.)
   * Used to check user approval status without making function calls
   *
   * @returns Promise with user claims or null if not available
   */
  async getCurrentUserClaims(): Promise<any> {
    try {
      const user = this.auth.currentUser;
      if (!user) {
        console.log('No authenticated user found');
        return null;
      }

      // Get fresh token with claims
      const idTokenResult = await user.getIdTokenResult(true);
      return idTokenResult.claims;
    } catch (error) {
      console.error('Error getting user claims:', error);
      return null;
    }
  }

  /**
   * Gets the current user's registration status
   * Users can only check their own status
   * Includes retry logic and authentication verification
   *
   * @returns Promise with registration status
   */
  async getCurrentUserRegistrationStatus(): Promise<RegistrationStatus> {
    try {
      // Ensure user is authenticated
      const user = this.auth.currentUser;
      if (!user) {
        throw new Error(
          'User must be authenticated to check registration status'
        );
      }

      // Wait for auth state to be fully loaded if needed
      await this.waitForAuthState();

      console.log('Getting registration status for user:', user.uid);

      // Use HTTP version as primary method since onCall has authentication issues
      console.log('Using HTTP method as primary for getRegistrationStatus...');
      const status = await this.getRegistrationStatusHttp();
      return status;
    } catch (error: any) {
      console.error('Error getting registration status via HTTP:', error);

      // Fallback to onCall method if HTTP fails
      console.log('HTTP method failed, trying onCall fallback...');
      try {
        const getRegistrationStatus = httpsCallable(
          this.functions,
          'getRegistrationStatus'
        );
        const result = await getRegistrationStatus({});

        const status = result.data as RegistrationStatus;
        this.userRegistrationStatusSubject.next(status);

        console.log('User registration status via onCall fallback:', status);
        return status;
      } catch (onCallError) {
        console.error('onCall fallback also failed:', onCallError);

        // Last resort: try token refresh and retry HTTP
        try {
          console.log('Attempting token refresh and HTTP retry...');
          return await this.getCurrentUserRegistrationStatusWithRetry();
        } catch (retryError) {
          console.error('All methods failed:', {
            originalError: error,
            onCallError,
            retryError,
          });
          throw retryError;
        }
      }
    }
  }

  /**
   * Gets all pending registrations (Admin only)
   * Returns current snapshot without setting up listener
   *
   * @returns Observable of pending registrations
   */
  getPendingRegistrations(): Observable<PendingRegistration[]> {
    return this.pendingRegistrations$;
  }

  /**
   * Checks if current user has admin privileges
   * Used to determine if admin features should be shown
   *
   * @returns Promise<boolean> - true if user is admin
   */
  async isCurrentUserAdmin(): Promise<boolean> {
    try {
      const user = this.auth.currentUser;
      if (!user) return false;

      const tokenResult = await user.getIdTokenResult();
      return tokenResult.claims?.['role'] === 'admin';
    } catch (error) {
      console.error('Error checking admin status:', error);
      return false;
    }
  }

  /**
   * Checks if current user's registration is approved
   * Used by route guards to control access
   *
   * @returns Promise<boolean> - true if user is approved
   */
  async isCurrentUserApproved(): Promise<boolean> {
    try {
      const user = this.auth.currentUser;
      if (!user) return false;

      const tokenResult = await user.getIdTokenResult();
      return tokenResult.claims?.['status'] === 'approved';
    } catch (error) {
      console.error('Error checking approval status:', error);
      return false;
    }
  }

  /**
   * Creates a new admin user (Admin only)
   * Only existing admins can create new admin users
   * Uses HTTP as primary method with onCall fallback
   *
   * @param adminData - Admin user data
   * @returns Promise with creation result
   */
  async createAdminUser(adminData: {
    email: string;
    password: string;
    name: string;
  }): Promise<any> {
    try {
      // Use HTTP version as primary method
      console.log('Attempting to create admin user via HTTP...');
      const result = await this.createAdminUserHttp(adminData);
      console.log('Admin user created via HTTP:', result);
      return result;
    } catch (error: any) {
      console.error('Error creating admin user via HTTP:', error);

      // Fallback to onCall method
      console.log('HTTP method failed, trying onCall fallback...');
      try {
        const createAdminUser = httpsCallable(
          this.functions,
          'createAdminUser'
        );
        const result = await createAdminUser(adminData);

        console.log('Admin user created via onCall fallback:', result.data);
        return result.data;
      } catch (onCallError) {
        console.error('Both HTTP and onCall methods failed:', {
          httpError: error,
          onCallError,
        });
        throw onCallError;
      }
    }
  }

  /**
   * HTTP fallback method for creating pending registration
   * Used when the onCall version fails due to CORS issues
   *
   * @param userData - User registration data
   * @returns Promise with registration result
   */
  private async createPendingRegistrationHttp(userData: {
    uid: string;
    email: string;
    name: string;
    role: string;
    verificationData?: any;
  }): Promise<any> {
    const user = this.auth.currentUser;
    if (!user) {
      throw new Error('User must be authenticated to create registration');
    }

    // Get user's ID token for authentication
    const idToken = await user.getIdToken();

    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      Authorization: `Bearer ${idToken}`,
    });

    const url = `https://${this.FIREBASE_REGION}-${this.FIREBASE_PROJECT_ID}.cloudfunctions.net/createPendingRegistrationHttp`;

    console.log('Creating pending registration via HTTP:', url);

    const response = await this.http
      .post(url, userData, { headers })
      .toPromise();

    console.log('Pending registration created via HTTP:', response);
    return response;
  }

  /**
   * HTTP method for approving pending registration
   */
  private async approvePendingRegistrationHttp(
    registrationId: string
  ): Promise<any> {
    const user = this.auth.currentUser;
    if (!user) {
      throw new Error('User must be authenticated to approve registrations');
    }

    // Get user's ID token for authentication
    const idToken = await user.getIdToken();

    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      Authorization: `Bearer ${idToken}`,
    });

    const url = `https://${this.FIREBASE_REGION}-${this.FIREBASE_PROJECT_ID}.cloudfunctions.net/approvePendingRegistrationHttp`;

    console.log('Approving registration via HTTP:', url);

    const response = await this.http
      .post(url, { registrationId }, { headers })
      .toPromise();

    console.log('Registration approved via HTTP:', response);
    return response;
  }

  /**
   * HTTP method for rejecting pending registration
   */
  private async rejectPendingRegistrationHttp(
    registrationId: string,
    reason: string
  ): Promise<any> {
    const user = this.auth.currentUser;
    if (!user) {
      throw new Error('User must be authenticated to reject registrations');
    }

    // Get user's ID token for authentication
    const idToken = await user.getIdToken();

    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      Authorization: `Bearer ${idToken}`,
    });

    const url = `https://${this.FIREBASE_REGION}-${this.FIREBASE_PROJECT_ID}.cloudfunctions.net/rejectPendingRegistrationHttp`;

    console.log('Rejecting registration via HTTP:', url);

    const response = await this.http
      .post(url, { registrationId, reason }, { headers })
      .toPromise();

    console.log('Registration rejected via HTTP:', response);
    return response;
  }

  /**
   * Helper method to wait for auth state to be fully loaded
   */
  private waitForAuthState(): Promise<void> {
    return new Promise((resolve) => {
      if (this.auth.currentUser) {
        resolve();
        return;
      }

      const unsubscribe = this.auth.onAuthStateChanged((user) => {
        if (user) {
          unsubscribe();
          resolve();
        }
      });

      // Timeout after 5 seconds
      setTimeout(() => {
        unsubscribe();
        resolve();
      }, 5000);
    });
  }

  /**
   * HTTP fallback method for getting registration status
   * Used when the onCall version fails due to authentication issues
   */
  private async getRegistrationStatusHttp(): Promise<RegistrationStatus> {
    const user = this.auth.currentUser;
    if (!user) {
      throw new Error(
        'User must be authenticated to check registration status'
      );
    }

    // Get user's ID token for authentication
    const idToken = await user.getIdToken();

    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      Authorization: `Bearer ${idToken}`,
    });

    const url = `https://${this.FIREBASE_REGION}-${this.FIREBASE_PROJECT_ID}.cloudfunctions.net/getRegistrationStatusHttp`;

    console.log('Getting registration status via HTTP:', url);

    const response = (await this.http
      .get(url, { headers })
      .toPromise()) as RegistrationStatus;

    this.userRegistrationStatusSubject.next(response);
    console.log('Registration status retrieved via HTTP:', response);
    return response;
  }

  /**
   * Retry method with token refresh using HTTP
   */
  private async getCurrentUserRegistrationStatusWithRetry(): Promise<RegistrationStatus> {
    const user = this.auth.currentUser;
    if (!user) {
      throw new Error(
        'User must be authenticated to check registration status'
      );
    }

    // Force token refresh
    await user.getIdToken(true);

    // Small delay to ensure token is refreshed
    await new Promise((resolve) => setTimeout(resolve, 1000));

    console.log(
      'Retrying registration status check via HTTP after token refresh...'
    );

    // Use HTTP method for retry since it's more reliable
    const status = await this.getRegistrationStatusHttp();
    console.log('User registration status (HTTP retry):', status);
    return status;
  }

  /**
   * Initializes pending registrations observable using collectionData
   * This provides real-time updates for pending registrations
   */
  private initializePendingRegistrationsObservable(): void {
    const pendingRegistrationsCollection = collection(
      this.firestore,
      'pendingRegistrations'
    );

    // Query for pending registrations, ordered by submission date
    const q = query(
      pendingRegistrationsCollection,
      where('status', '==', 'pending'),
      orderBy('submittedAt', 'desc')
    );

    // Use collectionData for real-time updates
    this.pendingRegistrations$ = collectionData(q, { idField: 'uid' }) as Observable<PendingRegistration[]>;
  }



  /**
   * HTTP method for creating admin user
   */
  private async createAdminUserHttp(adminData: {
    email: string;
    password: string;
    name: string;
  }): Promise<any> {
    const user = this.auth.currentUser;
    if (!user) {
      throw new Error('User must be authenticated to create admin users');
    }

    // Get user's ID token for authentication
    const idToken = await user.getIdToken();

    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      Authorization: `Bearer ${idToken}`,
    });

    const url = `https://${this.FIREBASE_REGION}-${this.FIREBASE_PROJECT_ID}.cloudfunctions.net/createAdminUserHttp`;

    console.log('Creating admin user via HTTP:', url);

    const response = await this.http
      .post(url, adminData, { headers })
      .toPromise();

    console.log('Admin user created via HTTP:', response);
    return response;
  }
}
