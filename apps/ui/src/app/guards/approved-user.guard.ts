import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivateFn, Router, RouterStateSnapshot } from '@angular/router';
import { Auth } from '@angular/fire/auth';
import { PendingRegistrationService } from '../services/pending-registration.service';

/**
 * Guard to ensure only approved users can access protected routes
 * Redirects pending users to pending-approval page
 * Redirects rejected users to appropriate error page
 * Redirects unauthenticated users to login
 */
export const ApprovedUserGuard: CanActivateFn = async (
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot
): Promise<boolean> => {
  const auth = inject(Auth);
  const router = inject(Router);
  const pendingRegistrationService = inject(PendingRegistrationService);

  try {
    // Check if user is authenticated
    const user = auth.currentUser;
    if (!user) {
      console.log(
        'ApprovedUserGuard: No authenticated user, redirecting to login'
      );
      await router.navigate(['/auth/login']);
      return false;
    }

    // Get user's current claims to check approval status
    const claims = await pendingRegistrationService.getCurrentUserClaims();

    if (!claims) {
      console.log(
        'ApprovedUserGuard: No user claims found, redirecting to login'
      );
      await router.navigate(['/auth/login']);
      return false;
    }

    console.log('ApprovedUserGuard: User claims:', claims);

    // Check approval status
    const status = claims.status;
    const role = claims.role;

    if (status === 'approved' && role) {
      // User is approved and has a role - allow access
      console.log(
        `ApprovedUserGuard: User approved with role ${role}, allowing access`
      );
      return true;
    } else if (status === 'pending') {
      // User is pending approval - redirect to pending page
      console.log(
        'ApprovedUserGuard: User pending approval, redirecting to pending-approval'
      );
      await router.navigate(['/auth/pending-approval']);
      return false;
    } else if (status === 'rejected') {
      // User is rejected - redirect to pending page (which will show rejection info)
      console.log(
        'ApprovedUserGuard: User rejected, redirecting to pending-approval'
      );
      await router.navigate(['/auth/pending-approval']);
      return false;
    } else {
      // Unknown status or no status - redirect to pending approval to check
      console.log(
        'ApprovedUserGuard: Unknown status, redirecting to pending-approval for status check'
      );
      await router.navigate(['/auth/pending-approval']);
      return false;
    }
  } catch (error) {
    console.error(
      'ApprovedUserGuard: Error checking user approval status:',
      error
    );

    // On error, redirect to login for safety
    await router.navigate(['/auth/login']);
    return false;
  }
};

/**
 * Guard specifically for admin-only routes
 * Ensures user is both approved and has admin role
 */
export const AdminOnlyGuard: CanActivateFn = async (
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot
): Promise<boolean> => {
  const auth = inject(Auth);
  const router = inject(Router);
  const pendingRegistrationService = inject(PendingRegistrationService);

  try {
    // First check if user is authenticated
    const user = auth.currentUser;
    if (!user) {
      console.log(
        'AdminOnlyGuard: No authenticated user, redirecting to login'
      );
      await router.navigate(['/auth/login']);
      return false;
    }

    // Check if user is admin
    const isAdmin = await pendingRegistrationService.isCurrentUserAdmin();

    if (isAdmin) {
      console.log('AdminOnlyGuard: User is admin, allowing access');
      return true;
    } else {
      console.log(
        'AdminOnlyGuard: User is not admin, redirecting to dashboard'
      );
      await router.navigate(['/dashboard']);
      return false;
    }
  } catch (error) {
    console.error('AdminOnlyGuard: Error checking admin status:', error);

    // On error, redirect to dashboard for safety
    await router.navigate(['/dashboard']);
    return false;
  }
};

/**
 * Guard for role-specific routes
 * Ensures user has the required role and is approved
 */
export const RoleGuard: CanActivateFn = async (
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot
): Promise<boolean> => {
  const auth = inject(Auth);
  const router = inject(Router);
  const pendingRegistrationService = inject(PendingRegistrationService);

  try {
    // Get required roles from route data
    const requiredRoles: string[] = route.data['roles'] || [];

    if (requiredRoles.length === 0) {
      console.warn('RoleGuard: No required roles specified in route data');
      return true; // No role requirement specified
    }

    // Check if user is authenticated
    const user = auth.currentUser;
    if (!user) {
      console.log('RoleGuard: No authenticated user, redirecting to login');
      await router.navigate(['/auth/login']);
      return false;
    }

    // Get user claims
    const claims = await pendingRegistrationService.getCurrentUserClaims();

    if (!claims) {
      console.log('RoleGuard: No user claims found, redirecting to login');
      await router.navigate(['/auth/login']);
      return false;
    }

    // Check if user is approved
    if (claims.status !== 'approved') {
      console.log(
        'RoleGuard: User not approved, redirecting to pending-approval'
      );
      await router.navigate(['/auth/pending-approval']);
      return false;
    }

    // Check if user has required role
    const userRole = claims.role;
    const hasRequiredRole = requiredRoles.includes(userRole);

    if (hasRequiredRole) {
      console.log(
        `RoleGuard: User has required role ${userRole}, allowing access`
      );
      return true;
    } else {
      console.log(
        `RoleGuard: User role ${userRole} not in required roles ${requiredRoles.join(
          ', '
        )}, redirecting to dashboard`
      );
      await router.navigate(['/dashboard']);
      return false;
    }
  } catch (error) {
    console.error('RoleGuard: Error checking role permissions:', error);

    // On error, redirect to dashboard for safety
    await router.navigate(['/dashboard']);
    return false;
  }
};

/**
 * Guard for preventing approved users from accessing auth pages
 * Redirects approved users to dashboard if they try to access login/register
 */
export const RedirectApprovedGuard: CanActivateFn = async (
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot
): Promise<boolean> => {
  const auth = inject(Auth);
  const router = inject(Router);
  const pendingRegistrationService = inject(PendingRegistrationService);

  try {
    // Check if user is authenticated
    const user = auth.currentUser;
    if (!user) {
      // No user, allow access to auth pages
      return true;
    }

    // Check if user is approved
    const isApproved = await pendingRegistrationService.isCurrentUserApproved();

    if (isApproved) {
      console.log(
        'RedirectApprovedGuard: Approved user trying to access auth page, redirecting to dashboard'
      );
      await router.navigate(['/dashboard']);
      return false;
    } else {
      // User is not approved, allow access to auth pages
      return true;
    }
  } catch (error) {
    console.error(
      'RedirectApprovedGuard: Error checking approval status:',
      error
    );

    // On error, allow access to auth pages for safety
    return true;
  }
};
