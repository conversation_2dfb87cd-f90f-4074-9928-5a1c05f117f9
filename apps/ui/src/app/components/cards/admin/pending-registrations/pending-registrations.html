<!--&lt;!&ndash; Pending Registrations Table &ndash;&gt;-->
<!--<div class="card bg-base-100 shadow-xl">-->
<!--  <div class="card-body">-->
<!--    <div class="flex justify-between items-center mb-6">-->
<!--      <h2 class="card-title text-xl">Pending Registrations</h2>-->
<!--      <button-->
<!--        (click)="refreshPendingRegistrations()"-->
<!--        [class.loading]="isRefreshing()"-->
<!--        [disabled]="isRefreshing()"-->
<!--        class="btn btn-sm btn-outline btn-primary">-->
<!--        @if (!isRefreshing()) {-->
<!--          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"-->
<!--               stroke="currentColor">-->
<!--            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"-->
<!--                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />-->
<!--          </svg>-->
<!--          Refresh-->
<!--        } @else {-->
<!--          Refreshing...-->
<!--        }-->
<!--      </button>-->
<!--    </div>-->

<!--    &lt;!&ndash; Loading State &ndash;&gt;-->
<!--    @if (isLoading()) {-->
<!--      <div class="flex justify-center items-center py-12">-->
<!--        <div class="loading loading-spinner loading-lg"></div>-->
<!--        <span class="ml-4 text-lg">Loading pending registrations...</span>-->
<!--      </div>-->
<!--    } @else {-->

<!--      &lt;!&ndash; No Pending Registrations &ndash;&gt;-->
<!--      @if ((pendingRegistrations$ | async)?.length === 0) {-->
<!--        <div class="text-center py-12">-->
<!--          <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-base-content/30 mb-4" fill="none"-->
<!--               viewBox="0 0 24 24" stroke="currentColor">-->
<!--            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"-->
<!--                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />-->
<!--          </svg>-->
<!--          <h3 class="text-lg font-medium text-base-content/70 mb-2">All caught up!</h3>-->
<!--          <p class="text-base-content/50">No pending registrations to review.</p>-->
<!--        </div>-->
<!--      } @else {-->

<!--        &lt;!&ndash; Registrations Table &ndash;&gt;-->
<!--        <div class="overflow-x-auto">-->
<!--          <table class="table table-zebra w-full">-->
<!--            <thead>-->
<!--            <tr>-->
<!--              <th>Name</th>-->
<!--              <th>Email</th>-->
<!--              <th>Role</th>-->
<!--              <th>Submitted</th>-->
<!--              <th>Verification</th>-->
<!--              <th class="text-center">Actions</th>-->
<!--            </tr>-->
<!--            </thead>-->
<!--            <tbody>-->
<!--              @for (registration of pendingRegistrations$ | async; track registration.uid) {-->
<!--                <tr>-->
<!--                  &lt;!&ndash; Name &ndash;&gt;-->
<!--                  <td>-->
<!--                    <div class="font-medium">{{ registration.name }}</div>-->
<!--                  </td>-->

<!--                  &lt;!&ndash; Email &ndash;&gt;-->
<!--                  <td>-->
<!--                    <div class="text-sm">{{ registration.email }}</div>-->
<!--                  </td>-->

<!--                  &lt;!&ndash; Role &ndash;&gt;-->
<!--                  <td>-->
<!--                    <div class="badge badge-outline">-->
<!--                      {{ getRoleDisplayName(registration.role) }}-->
<!--                    </div>-->
<!--                  </td>-->

<!--                  &lt;!&ndash; Submitted Date &ndash;&gt;-->
<!--                  <td>-->
<!--                    <div class="text-sm">{{ formatDate(registration.submittedAt) }}</div>-->
<!--                  </td>-->

<!--                  &lt;!&ndash; Verification Requirements &ndash;&gt;-->
<!--                  <td>-->
<!--                    <div class="text-xs">-->
<!--                      @if (registration.verificationData?.['requiresCredentials']) {-->
<!--                        <div class="badge badge-warning badge-xs mb-1">Credentials Required</div>-->
<!--                      }-->
<!--                      @if (registration.verificationData?.['requiresInstitutionalEmail']) {-->
<!--                        <div class="badge badge-info badge-xs mb-1">Institutional Email</div>-->
<!--                      }-->
<!--                      @if (registration.verificationData?.['requiresStudentLinkage']) {-->
<!--                        <div class="badge badge-secondary badge-xs mb-1">Student Link Required</div>-->
<!--                      }-->
<!--                      @if (registration.verificationData?.['requiresExperienceVerification']) {-->
<!--                        <div class="badge badge-primary badge-xs mb-1">Experience Required</div>-->
<!--                      }-->
<!--                      @if (registration.verificationData?.['requiresDepartmentVerification']) {-->
<!--                        <div class="badge badge-accent badge-xs mb-1">Department Verification</div>-->
<!--                      }-->
<!--                      @if (!hasVerificationRequirements(registration)) {-->
<!--                        <div class="text-base-content/50">Basic verification</div>-->
<!--                      }-->
<!--                    </div>-->
<!--                  </td>-->

<!--                  &lt;!&ndash; Actions &ndash;&gt;-->
<!--                  <td>-->
<!--                    <div class="flex justify-center gap-2">-->
<!--                      &lt;!&ndash; Approve Button &ndash;&gt;-->
<!--                      <button-->
<!--                        class="btn btn-success btn-sm"-->
<!--                        [disabled]="isProcessing(registration.uid)"-->
<!--                        (click)="approveRegistration(registration)">-->
<!--                        @if (isProcessing(registration.uid)) {-->
<!--                          <span class="loading loading-spinner loading-xs"></span>-->
<!--                        } @else {-->
<!--                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"-->
<!--                               stroke="currentColor">-->
<!--                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"-->
<!--                                  d="M5 13l4 4L19 7" />-->
<!--                          </svg>-->
<!--                        }-->
<!--                        Approve-->
<!--                      </button>-->

<!--                      &lt;!&ndash; Reject Button &ndash;&gt;-->
<!--                      <button-->
<!--                        class="btn btn-error btn-sm"-->
<!--                        [disabled]="isProcessing(registration.uid)"-->
<!--                        (click)="openRejectModal(registration)">-->
<!--                        @if (isProcessing(registration.uid)) {-->
<!--                          <span class="loading loading-spinner loading-xs"></span>-->
<!--                        } @else {-->
<!--                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"-->
<!--                               stroke="currentColor">-->
<!--                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"-->
<!--                                  d="M6 18L18 6M6 6l12 12" />-->
<!--                          </svg>-->
<!--                        }-->
<!--                        Reject-->
<!--                      </button>-->
<!--                    </div>-->
<!--                  </td>-->
<!--                </tr>-->
<!--              }-->
<!--            </tbody>-->
<!--          </table>-->
<!--        </div>-->
<!--      }-->
<!--    }-->
<!--  </div>-->
<!--</div>-->


{{ pendingRegistrations$ | async | json }}
