<!-- Pending Registrations Table -->
<div class="card bg-base-100 shadow-xl">
  <div class="card-body">
    <div class="flex justify-between items-center mb-6">
      <h2 class="card-title text-xl">Pending Registrations</h2>
    </div>


    @if ((pendingRegistrations$ | async)?.length === 0) {
      <div class="text-center py-12">
        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-base-content/30 mb-4" fill="none"
             viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <h3 class="text-lg font-medium text-base-content/70 mb-2">All caught up!</h3>
        <p class="text-base-content/50">No pending registrations to review.</p>
      </div>
    } @else {

      <!-- Registrations Table -->
      <div class="overflow-x-auto">
        <table class="table table-zebra w-full">
          <thead>
          <tr>
            <th>Name</th>
            <th>Email</th>
            <th>Role</th>
            <th>Submitted</th>
            <th>Verification</th>
            <th class="text-center">Actions</th>
          </tr>
          </thead>
          <tbody>
            @if (pendingRegistrations$ | async; as pendingRegistrations) {

              @for (registration of pendingRegistrations; track registration.uid) {
                <tr>
                  <!-- Name -->
                  <td>
                    <div class="font-medium">{{ registration.name }}</div>
                  </td>

                  <!-- Email -->
                  <td>
                    <div class="text-sm">{{ registration.email }}</div>
                  </td>

                  <!-- Role -->
                  <td>
                    <div class="badge badge-outline">
                      {{ (registration.role) }}
                    </div>
                  </td>

                  <!-- Submitted Date -->
                  <td>
                    <div class="text-sm">{{ formatDate(registration.submittedAt) }}</div>
                  </td>

                  <!-- Verification Requirements -->
                  <td>
                    <div class="text-xs">
                      @if (registration.verificationData?.['requiresCredentials']) {
                        <div class="badge badge-warning badge-xs mb-1">Credentials Required</div>
                      }
                      @if (registration.verificationData?.['requiresInstitutionalEmail']) {
                        <div class="badge badge-info badge-xs mb-1">Institutional Email</div>
                      }
                      @if (registration.verificationData?.['requiresStudentLinkage']) {
                        <div class="badge badge-secondary badge-xs mb-1">Student Link Required</div>
                      }
                      @if (registration.verificationData?.['requiresExperienceVerification']) {
                        <div class="badge badge-primary badge-xs mb-1">Experience Required</div>
                      }
                      @if (registration.verificationData?.['requiresDepartmentVerification']) {
                        <div class="badge badge-accent badge-xs mb-1">Department Verification</div>
                      }
                      @if (!hasVerificationRequirements(registration)) {
                        <div class="text-base-content/50">Basic verification</div>
                      }
                    </div>
                  </td>

                  <!-- Actions -->
                  <td>
                    <div class="flex justify-center gap-2">
                      <!-- Approve Button -->
                      <button
                        class="btn btn-success btn-sm"
                        [disabled]="isProcessing(registration.uid)"
                        (click)="approveRegistration(registration)">
                        @if (isProcessing(registration.uid)) {
                          <span class="loading loading-spinner loading-xs"></span>
                        } @else {
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                               stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M5 13l4 4L19 7" />
                          </svg>
                        }
                        Approve
                      </button>

                      <!-- Reject Button -->
                      <button
                        class="btn btn-error btn-sm"
                        [disabled]="isProcessing(registration.uid)"
                        (click)="openRejectModal(registration)">
                        @if (isProcessing(registration.uid)) {
                          <span class="loading loading-spinner loading-xs"></span>
                        } @else {
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                               stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        }
                        Reject
                      </button>
                    </div>
                  </td>
                </tr>
              }
            }
          </tbody>
        </table>
      </div>
    }
  </div>
</div>

<!-- Rejection Modal -->
@if (showRejectModal()) {
  <div class="modal modal-open">
    <div class="modal-box">
      <h3 class="font-bold text-lg mb-4">Reject Registration</h3>

      @if (selectedRegistration()) {
        <div class="mb-4">
          <p class="text-sm text-base-content/70 mb-2">
            You are about to reject the registration for:
          </p>
          <div class="bg-base-200 p-3 rounded">
            <div><strong>Name:</strong> {{ selectedRegistration()?.name }}</div>
            <div><strong>Email:</strong> {{ selectedRegistration()?.email }}</div>
            <div><strong>Role:</strong> {{ getRoleDisplayName(selectedRegistration()?.role || '') }}</div>
          </div>
        </div>
      }

      <div class="form-control mb-4">
        <label class="label">
          <span class="label-text">Reason for rejection <span class="text-error">*</span></span>
        </label>
        <textarea
          class="textarea textarea-bordered h-24"
          placeholder="Please provide a clear reason for rejecting this registration..."
          [(ngModel)]="rejectionReason"
          [class.textarea-error]="rejectionReason.trim().length === 0 && rejectionAttempted()">
        </textarea>
        @if (rejectionReason.trim().length === 0 && rejectionAttempted()) {
          <label class="label">
            <span class="label-text-alt text-error">Rejection reason is required</span>
          </label>
        }
      </div>

      <div class="modal-action">
        <button
          class="btn btn-ghost"
          (click)="closeRejectModal()"
          [disabled]="isRejecting()">
          Cancel
        </button>
        <button
          class="btn btn-error"
          [class.loading]="isRejecting()"
          [disabled]="isRejecting()"
          (click)="confirmRejectRegistration()">
          @if (!isRejecting()) {
            Reject Registration
          } @else {
            Rejecting...
          }
        </button>
      </div>
    </div>
  </div>
}
