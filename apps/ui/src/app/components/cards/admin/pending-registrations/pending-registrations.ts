import { Component, inject, OnInit } from '@angular/core';
import { collection, collectionData, Firestore } from '@angular/fire/firestore';
import { Observable } from 'rxjs';
import { AsyncPipe, JsonPipe } from '@angular/common';
import {
  PendingRegistration,
  PendingRegistrationService,
} from '../../../../services/pending-registration.service';
import { toastConfig$ } from '../../../../store/signals';
import { ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-pending-registrations',
  imports: [AsyncPipe, JsonPipe, ReactiveFormsModule],
  templateUrl: './pending-registrations.html',
})
export class PendingRegistrations implements OnInit {
  private firestore: Firestore = inject(Firestore);
  private pendingRegistrationService: PendingRegistrationService = inject(
    PendingRegistrationService
  );
  private processingRegistrations = new Set<string>();
  protected pendingRegistrations$: Observable<any[]> | undefined;

  ngOnInit() {
    this.pendingRegistrations$ = collectionData(
      collection(this.firestore, 'pendingRegistrations')
    );
  }

  /**
   * Formats timestamp for display
   * Handles multiple timestamp formats from Firestore
   */
  formatDate(timestamp: any): string {
    if (!timestamp) return 'Unknown';

    try {
      let date: Date;

      // Handle different timestamp formats
      if (timestamp.toDate) {
        // Firestore Timestamp object (from onCall functions)
        date = timestamp.toDate();
      } else if (timestamp._seconds !== undefined) {
        // Serialized Firestore Timestamp (from HTTP endpoints)
        date = new Date(
          timestamp._seconds * 1000 + (timestamp._nanoseconds || 0) / 1000000
        );
      } else if (typeof timestamp === 'number') {
        // Unix timestamp in milliseconds
        date = new Date(timestamp);
      } else if (typeof timestamp === 'string') {
        // ISO string or other string format
        date = new Date(timestamp);
      } else {
        // Try to create date directly
        date = new Date(timestamp);
      }

      // Validate the date
      if (isNaN(date.getTime())) {
        console.warn('Invalid date created from timestamp:', timestamp);
        return 'Invalid Date';
      }

      return (
        date.toLocaleDateString() +
        ' ' +
        date.toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
        })
      );
    } catch (error) {
      console.error('Error formatting date:', error, 'Timestamp:', timestamp);
      return 'Invalid Date';
    }
  }

  /**
   * Checks if registration has verification requirements
   */
  hasVerificationRequirements(registration: PendingRegistration): boolean {
    const verificationData: any = registration.verificationData;
    if (!verificationData) return false;

    return !!(
      verificationData['requiresCredentials'] ||
      verificationData['requiresInstitutionalEmail'] ||
      verificationData['requiresStudentLinkage'] ||
      verificationData['requiresExperienceVerification'] ||
      verificationData['requiresDepartmentVerification'] ||
      verificationData['requiresStudentId']
    );
  }

  /**
   * Approves a pending registration
   * Grants user access with their requested role
   */
  async approveRegistration(registration: PendingRegistration) {
    if (this.isProcessing(registration.uid)) return;

    try {
      this.processingRegistrations.add(registration.uid);

      console.log('Approving registration for:', registration.name);

      const result =
        await this.pendingRegistrationService.approvePendingRegistration(
          registration.uid
        );

      toastConfig$.set({
        status: 'success',
        message: `Registration approved for ${registration.name}`,
      });

      console.log('Registration approved successfully:', result);
    } catch (error: any) {
      console.error('Error approving registration:', error);

      let errorMessage = 'Failed to approve registration';
      if (error.message) {
        errorMessage = error.message;
      }

      toastConfig$.set({
        status: 'error',
        message: errorMessage,
      });
    } finally {
      this.processingRegistrations.delete(registration.uid);
    }
  }

  /**
   * Checks if a registration is currently being processed
   */
  isProcessing(uid: string): boolean {
    return this.processingRegistrations.has(uid);
  }

  /**
   * Opens the rejection modal for a registration
   */
  openRejectModal(registration: PendingRegistration) {
    this.selectedRegistration.set(registration);
    this.rejectionReason = '';
    this.rejectionAttempted.set(false);
    this.showRejectModal.set(true);
  }

  /**
   * Closes the rejection modal
   */
  closeRejectModal() {
    this.showRejectModal.set(false);
    this.selectedRegistration.set(null);
    this.rejectionReason = '';
    this.rejectionAttempted.set(false);
  }
}
