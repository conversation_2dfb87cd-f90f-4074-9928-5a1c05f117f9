import { Component, inject, OnInit, signal } from '@angular/core';
import { Observable } from 'rxjs';
import { AsyncPipe, CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  PendingRegistration,
  PendingRegistrationService,
} from '../../../../services/pending-registration.service';
import { toastConfig$ } from '../../../../store/signals';

@Component({
  selector: 'app-pending-registrations',
  imports: [AsyncPipe, CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './pending-registrations.html',
})
export class PendingRegistrations implements OnInit {
  private pendingRegistrationService: PendingRegistrationService = inject(
    PendingRegistrationService
  );

  // Component state signals
  isLoading = signal(true);
  isRefreshing = signal(false);
  showRejectModal = signal(false);
  selectedRegistration = signal<PendingRegistration | null>(null);
  isRejecting = signal(false);
  rejectionAttempted = signal(false);

  // Rejection modal data
  rejectionReason = '';

  // Processing state for individual registrations
  private processingRegistrations = new Set<string>();

  // Observable for pending registrations (real-time updates)
  protected pendingRegistrations$: Observable<PendingRegistration[]>;

  constructor() {
    // Initialize the pending registrations observable using the service
    this.pendingRegistrations$ = this.pendingRegistrationService.getPendingRegistrations();
  }

  async ngOnInit() {
    await this.initializeComponent();
  }

  /**
   * Formats timestamp for display
   * Handles multiple timestamp formats from Firestore
   */
  formatDate(timestamp: any): string {
    if (!timestamp) return 'Unknown';

    try {
      let date: Date;

      // Handle different timestamp formats
      if (timestamp.toDate) {
        // Firestore Timestamp object (from onCall functions)
        date = timestamp.toDate();
      } else if (timestamp._seconds !== undefined) {
        // Serialized Firestore Timestamp (from HTTP endpoints)
        date = new Date(
          timestamp._seconds * 1000 + (timestamp._nanoseconds || 0) / 1000000
        );
      } else if (typeof timestamp === 'number') {
        // Unix timestamp in milliseconds
        date = new Date(timestamp);
      } else if (typeof timestamp === 'string') {
        // ISO string or other string format
        date = new Date(timestamp);
      } else {
        // Try to create date directly
        date = new Date(timestamp);
      }

      // Validate the date
      if (isNaN(date.getTime())) {
        console.warn('Invalid date created from timestamp:', timestamp);
        return 'Invalid Date';
      }

      return (
        date.toLocaleDateString() +
        ' ' +
        date.toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
        })
      );
    } catch (error) {
      console.error('Error formatting date:', error, 'Timestamp:', timestamp);
      return 'Invalid Date';
    }
  }

  /**
   * Checks if registration has verification requirements
   */
  hasVerificationRequirements(registration: PendingRegistration): boolean {
    const verificationData: any = registration.verificationData;
    if (!verificationData) return false;

    return !!(
      verificationData['requiresCredentials'] ||
      verificationData['requiresInstitutionalEmail'] ||
      verificationData['requiresStudentLinkage'] ||
      verificationData['requiresExperienceVerification'] ||
      verificationData['requiresDepartmentVerification'] ||
      verificationData['requiresStudentId']
    );
  }

  /**
   * Approves a pending registration
   * Grants user access with their requested role
   */
  async approveRegistration(registration: PendingRegistration) {
    if (this.isProcessing(registration.uid)) return;

    try {
      this.processingRegistrations.add(registration.uid);

      console.log('Approving registration for:', registration.name);

      const result =
        await this.pendingRegistrationService.approvePendingRegistration(
          registration.uid
        );

      toastConfig$.set({
        status: 'success',
        message: `Registration approved for ${registration.name}`,
      });

      console.log('Registration approved successfully:', result);
    } catch (error: any) {
      console.error('Error approving registration:', error);

      let errorMessage = 'Failed to approve registration';
      if (error.message) {
        errorMessage = error.message;
      }

      toastConfig$.set({
        status: 'error',
        message: errorMessage,
      });
    } finally {
      this.processingRegistrations.delete(registration.uid);
    }
  }

  /**
   * Checks if a registration is currently being processed
   */
  isProcessing(uid: string): boolean {
    return this.processingRegistrations.has(uid);
  }

  /**
   * Refreshes pending registrations manually
   */
  async refreshPendingRegistrations() {
    if (this.isRefreshing()) return;

    try {
      this.isRefreshing.set(true);
      // The observable will automatically update with new data
      // This is just for user feedback
      await new Promise((resolve) => setTimeout(resolve, 500));

      toastConfig$.set({
        status: 'info',
        message: 'Pending registrations refreshed',
      });
    } catch (error) {
      console.error('Error refreshing pending registrations:', error);
      toastConfig$.set({
        status: 'error',
        message: 'Failed to refresh data',
      });
    } finally {
      this.isRefreshing.set(false);
    }
  }

  /**
   * Opens the rejection modal for a registration
   */
  openRejectModal(registration: PendingRegistration) {
    this.selectedRegistration.set(registration);
    this.rejectionReason = '';
    this.rejectionAttempted.set(false);
    this.showRejectModal.set(true);
  }

  /**
   * Closes the rejection modal
   */
  closeRejectModal() {
    this.showRejectModal.set(false);
    this.selectedRegistration.set(null);
    this.rejectionReason = '';
    this.rejectionAttempted.set(false);
  }

  /**
   * Confirms and processes the rejection of a registration
   */
  async confirmRejectRegistration() {
    const registration = this.selectedRegistration();
    if (!registration) return;

    // Validate rejection reason
    if (this.rejectionReason.trim().length === 0) {
      this.rejectionAttempted.set(true);
      return;
    }

    try {
      this.isRejecting.set(true);

      console.log('Rejecting registration for:', registration.name);

      const result =
        await this.pendingRegistrationService.rejectPendingRegistration(
          registration.uid,
          this.rejectionReason.trim()
        );

      toastConfig$.set({
        status: 'info',
        message: `Registration rejected for ${registration.name}`,
      });

      console.log('Registration rejected successfully:', result);

      // Close modal
      this.closeRejectModal();
    } catch (error: any) {
      console.error('Error rejecting registration:', error);

      let errorMessage = 'Failed to reject registration';
      if (error.message) {
        errorMessage = error.message;
      }

      toastConfig$.set({
        status: 'error',
        message: errorMessage,
      });
    } finally {
      this.isRejecting.set(false);
    }
  }

  /**
   * Gets role display name
   */
  getRoleDisplayName(role: string): string {
    switch (role) {
      case 'student':
        return 'Student';
      case 'trainer':
        return 'Trainer';
      case 'parent':
        return 'Parent';
      case 'college-staff':
        return 'College Staff';
      case 'admin':
        return 'Administrator';
      default:
        return role;
    }
  }

  /**
   * Initializes the component
   * Checks admin permissions and loads initial data
   */
  private async initializeComponent() {
    try {
      // Verify admin permissions
      const isAdmin = await this.pendingRegistrationService.isCurrentUserAdmin();

      if (!isAdmin) {
        console.error('User is not an admin');
        toastConfig$.set({
          status: 'error',
          message: 'Access denied. Admin privileges required.',
        });
        return;
      }

      console.log('Pending registrations component initialized successfully');

      // Subscribe to pending registrations for real-time updates
      this.pendingRegistrations$.subscribe((registrations) => {
        console.log('Pending registrations updated:', registrations.length);
        this.isLoading.set(false);
      });
    } catch (error) {
      console.error('Error initializing pending registrations component:', error);
      toastConfig$.set({
        status: 'error',
        message: 'Failed to load pending registrations.',
      });
      this.isLoading.set(false);
    }
  }
}
