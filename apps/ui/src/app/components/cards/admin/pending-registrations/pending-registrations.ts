import { Component, inject, OnInit } from '@angular/core';
import { collection, collectionData, Firestore } from '@angular/fire/firestore';
import { Observable } from 'rxjs';
import { AsyncPipe, JsonPipe } from '@angular/common';

@Component({
  selector: 'app-pending-registrations',
  imports: [AsyncPipe, JsonPipe],
  templateUrl: './pending-registrations.html',
})
export class PendingRegistrations implements OnInit {
  private firestore: Firestore = inject(Firestore);
  protected pendingRegistrations$: Observable<any[]> | undefined;

  ngOnInit() {
    this.pendingRegistrations$ = collectionData(
      collection(this.firestore, 'pendingRegistrations')
    );
  }
}
