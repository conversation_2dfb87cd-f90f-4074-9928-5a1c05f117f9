<!-- Admin Dashboard with Pending Registrations Management -->
<div class="container mx-auto p-6">

  <!-- Dashboard Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-base-content">Admin Dashboard</h1>
    <p class="text-base-content/70 mt-2">Manage user registrations and system administration</p>
  </div>

  <!-- Stats Cards -->
  <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">

    <!-- Pending Registrations Count -->
    <div class="stat bg-base-100 shadow rounded-lg">
      <div class="stat-figure text-warning">
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" stroke-linecap="round" stroke-linejoin="round"
                stroke-width="2" />
        </svg>
      </div>
      <div class="stat-title">Pending Approvals</div>
      <div class="stat-value text-warning">{{ (pendingRegistrations$ | async)?.length || 0 }}</div>
      <div class="stat-desc">Awaiting review</div>
    </div>

    <!-- Total Users (placeholder for future implementation) -->
    <div class="stat bg-base-100 shadow rounded-lg">
      <div class="stat-figure text-primary">
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" stroke-linecap="round" stroke-linejoin="round"
                stroke-width="2" />
        </svg>
      </div>
      <div class="stat-title">Total Users</div>
      <div class="stat-value text-primary">{{ totalApprovedUsers() }}</div>
      <div class="stat-desc">Active accounts</div>
    </div>

    <!-- Approved Today (placeholder) -->
    <div class="stat bg-base-100 shadow rounded-lg">
      <div class="stat-figure text-success">
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke-linecap="round" stroke-linejoin="round"
                stroke-width="2" />
        </svg>
      </div>
      <div class="stat-title">Approved Today</div>
      <div class="stat-value text-success">{{ approvedToday() }}</div>
      <div class="stat-desc">New approvals</div>
    </div>

    <!-- Rejected Today (placeholder) -->
    <div class="stat bg-base-100 shadow rounded-lg">
      <div class="stat-figure text-error">
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" stroke-linecap="round" stroke-linejoin="round"
                stroke-width="2" />
        </svg>
      </div>
      <div class="stat-title">Rejected Today</div>
      <div class="stat-value text-error">{{ rejectedToday() }}</div>
      <div class="stat-desc">Declined requests</div>
    </div>
  </div>

  <!-- Create Admin User Section -->
  <div class="card bg-base-100 shadow-xl mb-8">
    <div class="card-body">
      <div class="flex justify-between items-center mb-4">
        <h2 class="card-title text-xl">Create Admin User</h2>
        <button
          (click)="toggleCreateAdminForm()"
          class="btn btn-sm btn-outline btn-secondary">
          @if (!showCreateAdminForm()) {
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
                 stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Admin
          } @else {
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
                 stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
            Cancel
          }
        </button>
      </div>

      @if (showCreateAdminForm()) {
        <div class="bg-base-200 rounded-lg p-4">
          <form [formGroup]="createAdminForm" (ngSubmit)="createAdmin()">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- Name Field -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text">Full Name *</span>
                </label>
                <input
                  type="text"
                  class="input input-bordered"
                  formControlName="name"
                  placeholder="Enter admin's full name"
                  [class.input-error]="createAdminForm.get('name')?.invalid && createAdminForm.get('name')?.touched">
                @if (createAdminForm.get('name')?.invalid && createAdminForm.get('name')?.touched) {
                  <label class="label">
                    <span class="label-text-alt text-error">Name is required</span>
                  </label>
                }
              </div>

              <!-- Email Field -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text">Email Address *</span>
                </label>
                <input
                  type="email"
                  class="input input-bordered"
                  formControlName="email"
                  placeholder="Enter admin's email"
                  [class.input-error]="createAdminForm.get('email')?.invalid && createAdminForm.get('email')?.touched">
                @if (createAdminForm.get('email')?.invalid && createAdminForm.get('email')?.touched) {
                  <label class="label">
                    <span class="label-text-alt text-error">Valid email is required</span>
                  </label>
                }
              </div>

              <!-- Password Field -->
              <div class="form-control md:col-span-2">
                <label class="label">
                  <span class="label-text">Password *</span>
                </label>
                <input
                  type="password"
                  class="input input-bordered"
                  formControlName="password"
                  placeholder="Enter secure password (min 6 characters)"
                  [class.input-error]="createAdminForm.get('password')?.invalid && createAdminForm.get('password')?.touched">
                @if (createAdminForm.get('password')?.invalid && createAdminForm.get('password')?.touched) {
                  <label class="label">
                    <span class="label-text-alt text-error">Password must be at least 6 characters</span>
                  </label>
                }
              </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end mt-6">
              <button
                type="submit"
                class="btn btn-primary"
                [class.loading]="isCreatingAdmin()"
                [disabled]="createAdminForm.invalid || isCreatingAdmin()">
                @if (!isCreatingAdmin()) {
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24"
                       stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                  </svg>
                  Create Admin User
                } @else {
                  Creating...
                }
              </button>
            </div>
          </form>
        </div>
      }
    </div>
  </div>

  <app-pending-registrations></app-pending-registrations>
<!--  &lt;!&ndash; Pending Registrations Table &ndash;&gt;-->
<!--  <div class="card bg-base-100 shadow-xl">-->
<!--    <div class="card-body">-->
<!--      <div class="flex justify-between items-center mb-6">-->
<!--        <h2 class="card-title text-xl">Pending Registrations</h2>-->
<!--        <button-->
<!--          (click)="refreshPendingRegistrations()"-->
<!--          [class.loading]="isRefreshing()"-->
<!--          [disabled]="isRefreshing()"-->
<!--          class="btn btn-sm btn-outline btn-primary">-->
<!--          @if (!isRefreshing()) {-->
<!--            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"-->
<!--                 stroke="currentColor">-->
<!--              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"-->
<!--                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />-->
<!--            </svg>-->
<!--            Refresh-->
<!--          } @else {-->
<!--            Refreshing...-->
<!--          }-->
<!--        </button>-->
<!--      </div>-->

<!--      &lt;!&ndash; Loading State &ndash;&gt;-->
<!--      @if (isLoading()) {-->
<!--        <div class="flex justify-center items-center py-12">-->
<!--          <div class="loading loading-spinner loading-lg"></div>-->
<!--          <span class="ml-4 text-lg">Loading pending registrations...</span>-->
<!--        </div>-->
<!--      } @else {-->

<!--        &lt;!&ndash; No Pending Registrations &ndash;&gt;-->
<!--        @if ((pendingRegistrations$ | async)?.length === 0) {-->
<!--          <div class="text-center py-12">-->
<!--            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-base-content/30 mb-4" fill="none"-->
<!--                 viewBox="0 0 24 24" stroke="currentColor">-->
<!--              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"-->
<!--                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />-->
<!--            </svg>-->
<!--            <h3 class="text-lg font-medium text-base-content/70 mb-2">All caught up!</h3>-->
<!--            <p class="text-base-content/50">No pending registrations to review.</p>-->
<!--          </div>-->
<!--        } @else {-->

<!--          &lt;!&ndash; Registrations Table &ndash;&gt;-->
<!--          <div class="overflow-x-auto">-->
<!--            <table class="table table-zebra w-full">-->
<!--              <thead>-->
<!--              <tr>-->
<!--                <th>Name</th>-->
<!--                <th>Email</th>-->
<!--                <th>Role</th>-->
<!--                <th>Submitted</th>-->
<!--                <th>Verification</th>-->
<!--                <th class="text-center">Actions</th>-->
<!--              </tr>-->
<!--              </thead>-->
<!--              <tbody>-->
<!--                @for (registration of pendingRegistrations$ | async; track registration.uid) {-->
<!--                  <tr>-->
<!--                    &lt;!&ndash; Name &ndash;&gt;-->
<!--                    <td>-->
<!--                      <div class="font-medium">{{ registration.name }}</div>-->
<!--                    </td>-->

<!--                    &lt;!&ndash; Email &ndash;&gt;-->
<!--                    <td>-->
<!--                      <div class="text-sm">{{ registration.email }}</div>-->
<!--                    </td>-->

<!--                    &lt;!&ndash; Role &ndash;&gt;-->
<!--                    <td>-->
<!--                      <div class="badge badge-outline">-->
<!--                        {{ getRoleDisplayName(registration.role) }}-->
<!--                      </div>-->
<!--                    </td>-->

<!--                    &lt;!&ndash; Submitted Date &ndash;&gt;-->
<!--                    <td>-->
<!--                      <div class="text-sm">{{ formatDate(registration.submittedAt) }}</div>-->
<!--                    </td>-->

<!--                    &lt;!&ndash; Verification Requirements &ndash;&gt;-->
<!--                    <td>-->
<!--                      <div class="text-xs">-->
<!--                        @if (registration.verificationData?.['requiresCredentials']) {-->
<!--                          <div class="badge badge-warning badge-xs mb-1">Credentials Required</div>-->
<!--                        }-->
<!--                        @if (registration.verificationData?.['requiresInstitutionalEmail']) {-->
<!--                          <div class="badge badge-info badge-xs mb-1">Institutional Email</div>-->
<!--                        }-->
<!--                        @if (registration.verificationData?.['requiresStudentLinkage']) {-->
<!--                          <div class="badge badge-secondary badge-xs mb-1">Student Link Required</div>-->
<!--                        }-->
<!--                        @if (registration.verificationData?.['requiresExperienceVerification']) {-->
<!--                          <div class="badge badge-primary badge-xs mb-1">Experience Required</div>-->
<!--                        }-->
<!--                        @if (registration.verificationData?.['requiresDepartmentVerification']) {-->
<!--                          <div class="badge badge-accent badge-xs mb-1">Department Verification</div>-->
<!--                        }-->
<!--                        @if (!hasVerificationRequirements(registration)) {-->
<!--                          <div class="text-base-content/50">Basic verification</div>-->
<!--                        }-->
<!--                      </div>-->
<!--                    </td>-->

<!--                    &lt;!&ndash; Actions &ndash;&gt;-->
<!--                    <td>-->
<!--                      <div class="flex justify-center gap-2">-->
<!--                        &lt;!&ndash; Approve Button &ndash;&gt;-->
<!--                        <button-->
<!--                          class="btn btn-success btn-sm"-->
<!--                          [disabled]="isProcessing(registration.uid)"-->
<!--                          (click)="approveRegistration(registration)">-->
<!--                          @if (isProcessing(registration.uid)) {-->
<!--                            <span class="loading loading-spinner loading-xs"></span>-->
<!--                          } @else {-->
<!--                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"-->
<!--                                 stroke="currentColor">-->
<!--                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"-->
<!--                                    d="M5 13l4 4L19 7" />-->
<!--                            </svg>-->
<!--                          }-->
<!--                          Approve-->
<!--                        </button>-->

<!--                        &lt;!&ndash; Reject Button &ndash;&gt;-->
<!--                        <button-->
<!--                          class="btn btn-error btn-sm"-->
<!--                          [disabled]="isProcessing(registration.uid)"-->
<!--                          (click)="openRejectModal(registration)">-->
<!--                          @if (isProcessing(registration.uid)) {-->
<!--                            <span class="loading loading-spinner loading-xs"></span>-->
<!--                          } @else {-->
<!--                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"-->
<!--                                 stroke="currentColor">-->
<!--                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"-->
<!--                                    d="M6 18L18 6M6 6l12 12" />-->
<!--                            </svg>-->
<!--                          }-->
<!--                          Reject-->
<!--                        </button>-->
<!--                      </div>-->
<!--                    </td>-->
<!--                  </tr>-->
<!--                }-->
<!--              </tbody>-->
<!--            </table>-->
<!--          </div>-->
<!--        }-->
<!--      }-->
<!--    </div>-->
<!--  </div>-->
</div>

<!-- Rejection Modal -->
@if (showRejectModal()) {
  <div class="modal modal-open">
    <div class="modal-box">
      <h3 class="font-bold text-lg mb-4">Reject Registration</h3>

      @if (selectedRegistration()) {
        <div class="mb-4">
          <p class="text-sm text-base-content/70 mb-2">
            You are about to reject the registration for:
          </p>
          <div class="bg-base-200 p-3 rounded">
            <div><strong>Name:</strong> {{ selectedRegistration()?.name }}</div>
            <div><strong>Email:</strong> {{ selectedRegistration()?.email }}</div>
            <div><strong>Role:</strong> {{ getRoleDisplayName(selectedRegistration()?.role || '') }}</div>
          </div>
        </div>
      }

      <div class="form-control mb-4">
        <label class="label">
          <span class="label-text">Reason for rejection <span class="text-error">*</span></span>
        </label>
        <textarea
          class="textarea textarea-bordered h-24"
          placeholder="Please provide a clear reason for rejecting this registration..."
          [(ngModel)]="rejectionReason"
          [class.textarea-error]="rejectionReason.trim().length === 0 && rejectionAttempted()">
        </textarea>
        @if (rejectionReason.trim().length === 0 && rejectionAttempted()) {
          <label class="label">
            <span class="label-text-alt text-error">Rejection reason is required</span>
          </label>
        }
      </div>

      <div class="modal-action">
        <button
          class="btn btn-ghost"
          (click)="closeRejectModal()"
          [disabled]="isRejecting()">
          Cancel
        </button>
        <button
          class="btn btn-error"
          [class.loading]="isRejecting()"
          [disabled]="isRejecting()"
          (click)="confirmRejectRegistration()">
          @if (!isRejecting()) {
            Reject Registration
          } @else {
            Rejecting...
          }
        </button>
      </div>
    </div>
  </div>
}
