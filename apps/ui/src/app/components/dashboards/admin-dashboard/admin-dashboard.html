<!-- Admin Dashboard with Pending Registrations Management -->
<div class="container mx-auto p-6">

  <!-- Dashboard Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-base-content">Admin Dashboard</h1>
    <p class="text-base-content/70 mt-2">Manage user registrations and system administration</p>
  </div>

  <!-- Stats Cards -->
  <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">

    <!-- Pending Registrations Count -->
    <div class="stat bg-base-100 shadow rounded-lg">
      <div class="stat-figure text-warning">
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" stroke-linecap="round" stroke-linejoin="round"
                stroke-width="2" />
        </svg>
      </div>
      <div class="stat-title">Pending Approvals</div>
      <div class="stat-value text-warning">{{ (pendingRegistrations$ | async)?.length || 0 }}</div>
      <div class="stat-desc">Awaiting review</div>
    </div>

    <!-- Total Users (placeholder for future implementation) -->
    <div class="stat bg-base-100 shadow rounded-lg">
      <div class="stat-figure text-primary">
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" stroke-linecap="round" stroke-linejoin="round"
                stroke-width="2" />
        </svg>
      </div>
      <div class="stat-title">Total Users</div>
      <div class="stat-value text-primary">{{ totalApprovedUsers() }}</div>
      <div class="stat-desc">Active accounts</div>
    </div>

    <!-- Approved Today (placeholder) -->
    <div class="stat bg-base-100 shadow rounded-lg">
      <div class="stat-figure text-success">
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke-linecap="round" stroke-linejoin="round"
                stroke-width="2" />
        </svg>
      </div>
      <div class="stat-title">Approved Today</div>
      <div class="stat-value text-success">{{ approvedToday() }}</div>
      <div class="stat-desc">New approvals</div>
    </div>

    <!-- Rejected Today (placeholder) -->
    <div class="stat bg-base-100 shadow rounded-lg">
      <div class="stat-figure text-error">
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" stroke-linecap="round" stroke-linejoin="round"
                stroke-width="2" />
        </svg>
      </div>
      <div class="stat-title">Rejected Today</div>
      <div class="stat-value text-error">{{ rejectedToday() }}</div>
      <div class="stat-desc">Declined requests</div>
    </div>
  </div>

  <!-- Create Admin User Section -->
  <div class="card bg-base-100 shadow-xl mb-8">
    <div class="card-body">
      <div class="flex justify-between items-center mb-4">
        <h2 class="card-title text-xl">Create Admin User</h2>
        <button
          (click)="toggleCreateAdminForm()"
          class="btn btn-sm btn-outline btn-secondary">
          @if (!showCreateAdminForm()) {
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
                 stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Admin
          } @else {
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
                 stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
            Cancel
          }
        </button>
      </div>

      @if (showCreateAdminForm()) {
        <div class="bg-base-200 rounded-lg p-4">
          <form [formGroup]="createAdminForm" (ngSubmit)="createAdmin()">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- Name Field -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text">Full Name *</span>
                </label>
                <input
                  type="text"
                  class="input input-bordered"
                  formControlName="name"
                  placeholder="Enter admin's full name"
                  [class.input-error]="createAdminForm.get('name')?.invalid && createAdminForm.get('name')?.touched">
                @if (createAdminForm.get('name')?.invalid && createAdminForm.get('name')?.touched) {
                  <label class="label">
                    <span class="label-text-alt text-error">Name is required</span>
                  </label>
                }
              </div>

              <!-- Email Field -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text">Email Address *</span>
                </label>
                <input
                  type="email"
                  class="input input-bordered"
                  formControlName="email"
                  placeholder="Enter admin's email"
                  [class.input-error]="createAdminForm.get('email')?.invalid && createAdminForm.get('email')?.touched">
                @if (createAdminForm.get('email')?.invalid && createAdminForm.get('email')?.touched) {
                  <label class="label">
                    <span class="label-text-alt text-error">Valid email is required</span>
                  </label>
                }
              </div>

              <!-- Password Field -->
              <div class="form-control md:col-span-2">
                <label class="label">
                  <span class="label-text">Password *</span>
                </label>
                <input
                  type="password"
                  class="input input-bordered"
                  formControlName="password"
                  placeholder="Enter secure password (min 6 characters)"
                  [class.input-error]="createAdminForm.get('password')?.invalid && createAdminForm.get('password')?.touched">
                @if (createAdminForm.get('password')?.invalid && createAdminForm.get('password')?.touched) {
                  <label class="label">
                    <span class="label-text-alt text-error">Password must be at least 6 characters</span>
                  </label>
                }
              </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end mt-6">
              <button
                type="submit"
                class="btn btn-primary"
                [class.loading]="isCreatingAdmin()"
                [disabled]="createAdminForm.invalid || isCreatingAdmin()">
                @if (!isCreatingAdmin()) {
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24"
                       stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                  </svg>
                  Create Admin User
                } @else {
                  Creating...
                }
              </button>
            </div>
          </form>
        </div>
      }
    </div>
  </div>

  <!-- Pending Registrations Component -->
  <app-pending-registrations></app-pending-registrations>

</div>
