import { Component, inject, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Observable } from 'rxjs';
import {
  PendingRegistration,
  PendingRegistrationService
} from '../../../services/pending-registration.service';
import { toastConfig$ } from '../../../store/signals';
import { PendingRegistrations } from '../../cards/admin/pending-registrations/pending-registrations';

@Component({
  selector: 'app-admin-dashboard',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    PendingRegistrations,
  ],
  templateUrl: './admin-dashboard.html',
})
export class AdminDashboard implements OnInit {
  // Component state signals
  isLoading = signal(true);
  // Create admin form state
  showCreateAdminForm = signal(false);
  isCreatingAdmin = signal(false);
  // Create admin form
  createAdminForm = new FormGroup({
    name: new FormControl('', [Validators.required]),
    email: new FormControl('', [Validators.required, Validators.email]),
    password: new FormControl('', [
      Validators.required,
      Validators.minLength(6),
    ]),
  });
  // Stats (placeholder values for now)
  totalApprovedUsers = signal(0);
  approvedToday = signal(0);
  rejectedToday = signal(0);
  private pendingRegistrationService: PendingRegistrationService = inject(
    PendingRegistrationService
  );

  // Observable for pending registrations count in stats
  pendingRegistrations$: Observable<PendingRegistration[]> = this.pendingRegistrationService.getPendingRegistrations();

  async ngOnInit() {
    await this.initializeDashboard();
  }

  /**
   * Toggles the create admin form visibility
   */
  toggleCreateAdminForm() {
    this.showCreateAdminForm.set(!this.showCreateAdminForm());
    if (!this.showCreateAdminForm()) {
      // Reset form when hiding
      this.createAdminForm.reset();
    }
  }

  /**
   * Creates a new admin user
   */
  async createAdmin() {
    if (this.createAdminForm.invalid || this.isCreatingAdmin()) return;

    try {
      this.isCreatingAdmin.set(true);

      const formData = this.createAdminForm.value;
      console.log('Creating admin user:', formData.email);

      const result = await this.pendingRegistrationService.createAdminUser({
        email: formData.email!,
        password: formData.password!,
        name: formData.name!,
      });

      toastConfig$.set({
        status: 'success',
        message: `Admin user created successfully for ${formData.name}`,
      });

      console.log('Admin user created successfully:', result);

      // Reset form and hide it
      this.createAdminForm.reset();
      this.showCreateAdminForm.set(false);
    } catch (error: any) {
      console.error('Error creating admin user:', error);

      let errorMessage = 'Failed to create admin user';
      if (error.message) {
        errorMessage = error.message;
      } else if (error.error?.error) {
        errorMessage = error.error.error;
      }

      toastConfig$.set({
        status: 'error',
        message: errorMessage,
      });
    } finally {
      this.isCreatingAdmin.set(false);
    }
  }

  /**
   * Initializes the admin dashboard
   * Checks admin permissions and loads initial data
   */
  private async initializeDashboard() {
    try {
      // Verify admin permissions
      const isAdmin =
        await this.pendingRegistrationService.isCurrentUserAdmin();

      if (!isAdmin) {
        console.error('User is not an admin');
        toastConfig$.set({
          status: 'error',
          message: 'Access denied. Admin privileges required.',
        });
        return;
      }

      console.log('Admin dashboard initialized successfully');
      this.isLoading.set(false);

      // Load additional stats (placeholder implementation)
      await this.loadDashboardStats();
    } catch (error) {
      console.error('Error initializing admin dashboard:', error);
      toastConfig$.set({
        status: 'error',
        message: 'Failed to load admin dashboard.',
      });
      this.isLoading.set(false);
    }
  }

  /**
   * Loads dashboard statistics
   * Placeholder implementation - can be expanded with real data
   */
  private async loadDashboardStats() {
    // TODO: Implement real statistics from backend
    // For now, using placeholder values
    this.totalApprovedUsers.set(42);
    this.approvedToday.set(3);
    this.rejectedToday.set(1);
  }
}
