# Firebase Backend Architecture

## Overview
The Worthy Freshers platform leverages Firebase as a comprehensive Backend-as-a-Service (BaaS) solution, providing authentication, database, cloud functions, storage, and hosting services. The backend is designed for scalability, security, and real-time data synchronization.

## Firebase Services Architecture

### Service Integration
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Firebase      │    │   Cloud         │    │   Firestore     │
│   Authentication│◄──►│   Functions     │◄──►│   Database      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Firebase      │    │   Firebase      │    │   Firebase      │
│   Storage       │    │   Hosting       │    │   Analytics     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Firebase Authentication

### Authentication Configuration
```typescript
// Firebase Auth setup
export const appConfig: ApplicationConfig = {
  providers: [
    provideFirebaseApp(() => initializeApp(environment.firebaseConfig)),
    provideAuth(() => getAuth()),
    // ... other providers
  ]
};

// Environment configuration
const firebaseConfig = {
  apiKey: "AIzaSyB03hVum4c_Q8hkYNbnRFbRZH5ox2HEMmw",
  authDomain: "worthy-freshers.firebaseapp.com",
  projectId: "worthy-freshers",
  storageBucket: "worthy-freshers.firebasestorage.app",
  messagingSenderId: "************",
  appId: "1:************:web:ae38ecaa63a04faffdf131",
  measurementId: "G-KYC66T1VLS"
};
```

### Custom Claims System
```typescript
// Custom claims structure
interface UserClaims {
  role: 'admin' | 'student' | 'trainer' | 'parent' | 'college-staff';
  status: 'pending' | 'approved' | 'rejected';
  approvedAt?: number;
  approvedBy?: string;
  registeredAt: number;
  createdByBootstrap?: boolean;
}

// Setting custom claims (Firebase Functions)
await admin.auth().setCustomUserClaims(uid, {
  role: 'student',
  status: 'approved',
  approvedAt: Date.now(),
  approvedBy: adminUid,
  registeredAt: Date.now()
});
```

### Authentication Methods
1. **Email/Password**: Primary authentication method
2. **Google OAuth**: Social login integration
3. **Custom Claims**: Role-based access control
4. **Token Refresh**: Automatic token management

## Cloud Functions

### Function Architecture
```
functions/firebase/src/
├── index.ts                    # Function exports
└── lib/
    ├── hello.ts               # Health check function
    ├── pending-registration.ts # Registration management
    └── update-user-role.ts    # Legacy role management
```

### Core Functions

#### 1. Pending Registration Functions
```typescript
// Create pending registration (onCall)
export const createPendingRegistration = functions.https.onCall(
  async (request: any, context: any) => {
    const { uid, email, name, role, verificationData } = request.data;

    // Validation
    if (!uid || !email || !name || !role) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Missing required fields'
      );
    }

    // Create registration record
    const pendingRegistration: PendingRegistrationData = {
      uid, email, name, role,
      status: 'pending',
      submittedAt: admin.firestore.Timestamp.now(),
      verificationData: verificationData || {}
    };

    // Store in Firestore
    await db.collection('pendingRegistrations').doc(uid).set(pendingRegistration);

    // Set pending status in custom claims
    await admin.auth().setCustomUserClaims(uid, {
      status: 'pending',
      registeredAt: Date.now()
    });

    return {
      success: true,
      message: 'Registration submitted successfully',
      registrationId: uid
    };
  }
);
```

#### 2. HTTP Fallback Functions
```typescript
// HTTP version for CORS handling
export const createPendingRegistrationHttp = functions.https.onRequest(
  async (req, res) => {
    // CORS handling
    if (req.method === 'OPTIONS') {
      res.set('Access-Control-Allow-Origin', '*');
      res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
      res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      res.status(204).send('');
      return;
    }

    // Authentication verification
    const authHeader = req.headers.authorization;
    if (!authHeader?.startsWith('Bearer ')) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }

    try {
      const token = authHeader.split('Bearer ')[1];
      const decodedToken = await admin.auth().verifyIdToken(token);
      
      // Process registration...
      const result = await processRegistration(req.body, decodedToken.uid);
      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);
```

#### 3. Admin Functions
```typescript
// Approve pending registration
export const approvePendingRegistration = functions.https.onCall(
  async (request: any, context: any) => {
    // Admin verification
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'Must be authenticated');
    }

    const adminToken = await admin.auth().getUser(context.auth.uid);
    const adminClaims = adminToken.customClaims;

    if (!adminClaims?.role || adminClaims.role !== 'admin') {
      throw new functions.https.HttpsError('permission-denied', 'Admin only');
    }

    const { registrationId } = request.data;
    
    // Get pending registration
    const pendingDoc = await db.collection('pendingRegistrations').doc(registrationId).get();
    if (!pendingDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Registration not found');
    }

    const pendingData = pendingDoc.data() as PendingRegistrationData;

    // Update registration status
    await db.collection('pendingRegistrations').doc(registrationId).update({
      status: 'approved',
      approvedBy: context.auth.uid,
      approvedAt: admin.firestore.Timestamp.now()
    });

    // Grant user access
    await admin.auth().setCustomUserClaims(pendingData.uid, {
      role: pendingData.role,
      status: 'approved',
      approvedAt: Date.now(),
      approvedBy: context.auth.uid
    });

    return {
      success: true,
      message: `Registration approved for ${pendingData.name}`,
      approvedUser: {
        uid: pendingData.uid,
        name: pendingData.name,
        email: pendingData.email,
        role: pendingData.role
      }
    };
  }
);
```

## Firestore Database

### Database Structure
```
worthy-freshers (project)
├── pendingRegistrations/       # User registration requests
│   └── {uid}/                 # Document per user
├── users/                     # Approved user profiles
│   └── {uid}/                 # User profile data
├── institutes/                # Educational institutions
│   └── {instituteId}/         # Institute details
├── courses/                   # Training courses
│   └── {courseId}/            # Course information
├── enrollments/               # Student-course relationships
│   └── {enrollmentId}/        # Enrollment records
├── auditLogs/                 # System activity tracking
│   └── {logId}/               # Audit log entries
├── systemConfig/              # Global configuration
│   └── {configId}/            # Configuration documents
├── help/                      # Contact form submissions
│   └── {helpId}/              # Help requests
└── approvedDomains/           # Email domain whitelist
    └── {domainId}/            # Approved domains
```

### Collection Schemas

#### pendingRegistrations
```typescript
interface PendingRegistration {
  uid: string;                    // Firebase Auth UID
  email: string;                  // User email
  name: string;                   // Display name
  role: UserRole;                 // Requested role
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: Timestamp;         // Registration timestamp
  verificationData?: {            // Role-specific data
    studentId?: string;
    institutionEmail?: string;
    credentials?: string[];
    parentStudentLink?: string;
    requiresCredentials?: boolean;
    requiresInstitutionalEmail?: boolean;
    requiresStudentLinkage?: boolean;
    requiresExperienceVerification?: boolean;
    requiresDepartmentVerification?: boolean;
    requiresStudentId?: boolean;
  };
  approvedBy?: string;            // Admin UID
  approvedAt?: Timestamp;         // Approval timestamp
  rejectedBy?: string;            // Admin UID
  rejectedAt?: Timestamp;         // Rejection timestamp
  rejectionReason?: string;       // Rejection reason
}
```

#### users
```typescript
interface User {
  uid: string;
  email: string;
  name: string;
  role: UserRole;
  status: 'approved';
  createdAt: Timestamp;
  lastLoginAt?: Timestamp;
  profile: {
    avatar?: string;
    bio?: string;
    phone?: string;
    address?: string;
    // Role-specific profile fields
  };
  preferences: {
    theme?: string;
    notifications?: boolean;
    language?: string;
  };
}
```

#### auditLogs
```typescript
interface AuditLog {
  id: string;
  timestamp: Timestamp;
  action: string;                 // Action performed
  performedBy: string;            // User UID
  targetUser?: string;            // Affected user UID
  details: {                      // Action-specific details
    previousState?: any;
    newState?: any;
    reason?: string;
  };
  ipAddress?: string;
  userAgent?: string;
}
```

## Security Rules

### Firestore Security Rules
```javascript
rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isApproved() {
      return isAuthenticated() && 
             request.auth.token.status == 'approved';
    }
    
    function isAdmin() {
      return isAuthenticated() && 
             request.auth.token.role == 'admin' && 
             request.auth.token.status == 'approved';
    }
    
    function hasRole(role) {
      return isApproved() && request.auth.token.role == role;
    }

    // Pending Registrations
    match /pendingRegistrations/{uid} {
      // Users can read their own registration
      allow read: if isAuthenticated() && request.auth.uid == uid;
      
      // Admins can read all registrations
      allow read: if isAdmin();
      
      // Only system functions can create registrations
      allow create: if false;
      
      // Only admins can update (approve/reject)
      allow update: if isAdmin();
      
      // No deletes allowed
      allow delete: if false;
    }

    // User Profiles
    match /users/{uid} {
      // Users can read their own profile
      allow read: if isAuthenticated() && request.auth.uid == uid;
      
      // Admins can read all profiles
      allow read: if isAdmin();
      
      // Users can update their own profile (limited fields)
      allow update: if isAuthenticated() && 
                       request.auth.uid == uid &&
                       onlyUpdatingAllowedFields();
      
      // Only system functions can create user profiles
      allow create: if false;
      
      // Only admins can delete profiles
      allow delete: if isAdmin();
    }

    // Audit Logs
    match /auditLogs/{logId} {
      // Only admins can read audit logs
      allow read: if isAdmin();
      
      // Only system functions can create logs
      allow create: if false;
      
      // No updates or deletes
      allow update, delete: if false;
    }

    // System Configuration
    match /systemConfig/{configId} {
      // Only admins can access system config
      allow read, write: if isAdmin();
    }

    // Help/Contact Forms
    match /help/{helpId} {
      // Anyone can create help requests
      allow create: if true;
      
      // Only admins can read help requests
      allow read: if isAdmin();
      
      // Only admins can update status
      allow update: if isAdmin();
      
      // No deletes
      allow delete: if false;
    }

    // Helper function for profile updates
    function onlyUpdatingAllowedFields() {
      let allowedFields = ['profile', 'preferences', 'lastLoginAt'];
      return request.resource.data.keys().hasOnly(allowedFields);
    }
  }
}
```

## Multi-Environment Setup

### Environment Configuration
```typescript
// Development environment
export const environment = {
  firebaseConfig: {
    apiKey: "AIzaSyB03hVum4c_Q8hkYNbnRFbRZH5ox2HEMmw",
    authDomain: "worthy-freshers.firebaseapp.com",
    projectId: "worthy-freshers",
    // ... other config
    appId: "1:************:web:4288b3b1a0194485fdf131",
    measurementId: "G-0M6FQTJKD8"
  }
};

// QA environment
export const environment = {
  firebaseConfig: {
    // Same project, different app ID
    appId: "1:************:web:d8d0e9c585b86543fdf131",
    measurementId: "G-BDCC9KM6YR"
  }
};

// Production environment
export const environment = {
  firebaseConfig: {
    // Same project, different app ID
    appId: "1:************:web:ae38ecaa63a04faffdf131",
    measurementId: "G-KYC66T1VLS"
  }
};
```

### Deployment Configuration
```json
// firebase.json
{
  "hosting": [
    {
      "target": "dev",
      "public": "dist/apps/ui",
      "ignore": ["firebase.json", "**/.*", "**/node_modules/**"],
      "rewrites": [
        {
          "source": "**",
          "destination": "/index.html"
        }
      ]
    },
    {
      "target": "qa",
      "public": "dist/apps/ui",
      "ignore": ["firebase.json", "**/.*", "**/node_modules/**"],
      "rewrites": [
        {
          "source": "**",
          "destination": "/index.html"
        }
      ]
    },
    {
      "target": "prod",
      "public": "dist/apps/ui",
      "ignore": ["firebase.json", "**/.*", "**/node_modules/**"],
      "rewrites": [
        {
          "source": "**",
          "destination": "/index.html"
        }
      ]
    }
  ],
  "functions": {
    "source": "dist/functions/firebase",
    "runtime": "nodejs18"
  },
  "firestore": {
    "rules": "firestore.rules",
    "indexes": "firestore.indexes.json"
  }
}
```

## Performance Optimizations

### Database Indexing
```json
// firestore.indexes.json
{
  "indexes": [
    {
      "collectionGroup": "pendingRegistrations",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "status", "order": "ASCENDING" },
        { "fieldPath": "submittedAt", "order": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "users",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "role", "order": "ASCENDING" },
        { "fieldPath": "createdAt", "order": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "auditLogs",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "performedBy", "order": "ASCENDING" },
        { "fieldPath": "timestamp", "order": "DESCENDING" }
      ]
    }
  ],
  "fieldOverrides": []
}
```

### Function Optimization
```typescript
// Optimized function with minimal cold start
export const optimizedFunction = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30
  })
  .https.onCall(async (data, context) => {
    // Minimal initialization
    // Efficient processing
    // Quick response
  });
```

## Monitoring & Analytics

### Firebase Analytics Events
```typescript
// Custom analytics events
import { Analytics, logEvent } from '@angular/fire/analytics';

// Track user registration
logEvent(analytics, 'user_registration', {
  method: 'email',
  role: 'student'
});

// Track admin actions
logEvent(analytics, 'admin_action', {
  action: 'approve_registration',
  target_role: 'student'
});
```

### Performance Monitoring
```typescript
// Performance monitoring setup
import { getPerformance } from '@angular/fire/performance';

export const appConfig: ApplicationConfig = {
  providers: [
    // ... other providers
    providePerformance(() => getPerformance()),
  ]
};
```

## Error Handling & Logging

### Function Error Handling
```typescript
export const robustFunction = functions.https.onCall(
  async (request, context) => {
    try {
      // Function logic
      return { success: true, data: result };
    } catch (error) {
      console.error('Function error:', error);
      
      // Log to external service if needed
      await logError(error, context);
      
      // Return appropriate error
      if (error instanceof functions.https.HttpsError) {
        throw error;
      }
      
      throw new functions.https.HttpsError(
        'internal',
        'An unexpected error occurred'
      );
    }
  }
);
```

### Client-side Error Handling
```typescript
// Service-level error handling with fallbacks
async createPendingRegistration(userData: any): Promise<any> {
  try {
    // Try primary method (onCall)
    const result = await this.createPendingRegistrationOnCall(userData);
    return result;
  } catch (error) {
    console.warn('Primary method failed, trying fallback:', error);
    
    try {
      // Fallback method (HTTP)
      return await this.createPendingRegistrationHttp(userData);
    } catch (fallbackError) {
      console.error('All methods failed:', { error, fallbackError });
      throw error; // Throw original error
    }
  }
}
```

---

*This document provides comprehensive details about the Firebase backend architecture and implementation in Worthy Freshers.*
