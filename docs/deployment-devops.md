# Deployment & DevOps Guide

## Overview
The Worthy Freshers platform uses a comprehensive DevOps pipeline with multi-environment deployment, automated CI/CD, and monitoring. The system is built on Firebase infrastructure with GitLab CI/CD integration.

## Environment Strategy

### Multi-Environment Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Development   │    │       QA        │    │   Production    │
│                 │    │                 │    │                 │
│ • Feature dev   │───►│ • Testing       │───►│ • Live system   │
│ • Local testing │    │ • Integration   │    │ • End users     │
│ • Rapid deploy  │    │ • Validation    │    │ • Stable builds │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Environment Configuration
```typescript
// Environment-specific configurations
environments/
├── environment.ts          # Default (development)
├── environment.dev.ts      # Development environment
├── environment.qa.ts       # QA environment
└── environment.prod.ts     # Production environment

// Firebase project structure
Firebase Project: worthy-freshers
├── Development App: 1:430652281252:web:4288b3b1a0194485fdf131
├── QA App:          1:430652281252:web:d8d0e9c585b86543fdf131
└── Production App:  1:430652281252:web:ae38ecaa63a04faffdf131
```

## Build System (Nx Monorepo)

### Project Structure
```
worthy-freshers/
├── apps/
│   └── ui/                 # Angular frontend application
├── functions/
│   └── firebase/           # Firebase Cloud Functions
├── libs/
│   └── util/              # Shared utilities
├── docs/                  # Documentation
├── nx.json               # Nx configuration
├── package.json          # Dependencies and scripts
└── firebase.json         # Firebase configuration
```

### Build Scripts
```json
{
  "scripts": {
    "build:dev": "ng build --configuration=dev",
    "build:qa": "ng build --configuration=qa",
    "build:prod": "ng build --configuration=prod",
    "build:functions": "nx run firebase:build",
    "deploy:dev": "npm run build:dev && firebase deploy --only hosting:dev",
    "deploy:qa": "npm run build:qa && firebase deploy --only hosting:qa",
    "deploy:prod": "npm run build:prod && firebase deploy --only hosting:prod",
    "deploy:functions": "firebase deploy --only functions",
    "build:deploy:functions": "npm run build:functions && npm run deploy:functions"
  }
}
```

### Angular Build Configurations
```json
// angular.json build configurations
"configurations": {
  "production": {
    "budgets": [
      {
        "type": "initial",
        "maximumWarning": "500kb",
        "maximumError": "1mb"
      },
      {
        "type": "anyComponentStyle",
        "maximumWarning": "2kb",
        "maximumError": "4kb"
      }
    ],
    "outputHashing": "all"
  },
  "dev": {
    "buildOptimizer": false,
    "optimization": false,
    "vendorChunk": true,
    "extractLicenses": false,
    "sourceMap": true,
    "namedChunks": true,
    "fileReplacements": [
      {
        "replace": "apps/ui/src/environments/environment.ts",
        "with": "apps/ui/src/environments/environment.dev.ts"
      }
    ]
  },
  "qa": {
    "buildOptimizer": true,
    "optimization": true,
    "vendorChunk": false,
    "extractLicenses": true,
    "sourceMap": false,
    "namedChunks": false,
    "fileReplacements": [
      {
        "replace": "apps/ui/src/environments/environment.ts",
        "with": "apps/ui/src/environments/environment.qa.ts"
      }
    ]
  }
}
```

## CI/CD Pipeline (GitLab CI)

### Pipeline Configuration
```yaml
# .gitlab-ci.yml
image: node:20

variables:
  CI: 'true'

# Main CI job
CI:
  interruptible: true
  only:
    - main
    - merge_requests
  script:
    # Install pnpm globally
    - npm install --prefix=$HOME/.local -g pnpm@8

    # Install dependencies
    - pnpm install --frozen-lockfile
    
    # Install Playwright for e2e tests
    - pnpm exec playwright install --with-deps

    # Run all tasks (lint, test, build, e2e)
    - pnpm exec nx run-many -t lint test build e2e
    
  after_script:
    # Nx Cloud CI optimization
    - pnpm exec nx fix-ci
```

### Pipeline Stages
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Install   │───►│    Lint     │───►│    Test     │───►│    Build    │
│             │    │             │    │             │    │             │
│ • pnpm      │    │ • ESLint    │    │ • Jest      │    │ • Angular   │
│ • deps      │    │ • Prettier  │    │ • Unit      │    │ • Functions │
│ • tools     │    │ • TypeScript│    │ • Coverage  │    │ • Optimize  │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
         │                                                       │
         ▼                                                       ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│     E2E     │───►│   Deploy    │───►│   Monitor   │───►│   Notify    │
│             │    │             │    │             │    │             │
│ • Playwright│    │ • Firebase  │    │ • Analytics │    │ • Slack     │
│ • Browser   │    │ • Multi-env │    │ • Errors    │    │ • Email     │
│ • Integration│    │ • Rollback  │    │ • Performance│    │ • Dashboard │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

## Firebase Deployment

### Firebase Configuration
```json
// firebase.json
{
  "hosting": [
    {
      "target": "dev",
      "public": "dist/apps/ui",
      "ignore": ["firebase.json", "**/.*", "**/node_modules/**"],
      "rewrites": [
        {
          "source": "**",
          "destination": "/index.html"
        }
      ],
      "headers": [
        {
          "source": "**/*.@(js|css)",
          "headers": [
            {
              "key": "Cache-Control",
              "value": "max-age=31536000"
            }
          ]
        }
      ]
    },
    {
      "target": "qa",
      "public": "dist/apps/ui",
      "ignore": ["firebase.json", "**/.*", "**/node_modules/**"],
      "rewrites": [
        {
          "source": "**",
          "destination": "/index.html"
        }
      ]
    },
    {
      "target": "prod",
      "public": "dist/apps/ui",
      "ignore": ["firebase.json", "**/.*", "**/node_modules/**"],
      "rewrites": [
        {
          "source": "**",
          "destination": "/index.html"
        }
      ],
      "headers": [
        {
          "source": "**/*.@(js|css)",
          "headers": [
            {
              "key": "Cache-Control",
              "value": "max-age=31536000"
            }
          ]
        },
        {
          "source": "**",
          "headers": [
            {
              "key": "X-Content-Type-Options",
              "value": "nosniff"
            },
            {
              "key": "X-Frame-Options",
              "value": "DENY"
            },
            {
              "key": "X-XSS-Protection",
              "value": "1; mode=block"
            }
          ]
        }
      ]
    }
  ],
  "functions": {
    "source": "dist/functions/firebase",
    "runtime": "nodejs18",
    "predeploy": [
      "npm run build:functions"
    ]
  },
  "firestore": {
    "rules": "firestore.rules",
    "indexes": "firestore.indexes.json"
  },
  "storage": {
    "rules": "storage.rules"
  }
}
```

### Deployment Commands
```bash
# Development deployment
npm run deploy:dev

# QA deployment
npm run deploy:qa

# Production deployment
npm run deploy:prod

# Functions only
npm run deploy:functions

# Full deployment (all environments)
firebase deploy --only hosting,functions,firestore:rules
```

## Environment Management

### Firebase Targets Setup
```bash
# Initialize Firebase targets
firebase target:apply hosting dev worthy-freshers-dev
firebase target:apply hosting qa worthy-freshers-qa
firebase target:apply hosting prod worthy-freshers-prod

# List configured targets
firebase target:list
```

### Environment Variables
```typescript
// Environment-specific configurations
interface Environment {
  production: boolean;
  firebaseConfig: {
    apiKey: string;
    authDomain: string;
    projectId: string;
    storageBucket: string;
    messagingSenderId: string;
    appId: string;
    measurementId: string;
  };
  apiUrl?: string;
  debugMode?: boolean;
  features?: {
    analytics: boolean;
    performance: boolean;
    crashlytics: boolean;
  };
}

// Development environment
export const environment: Environment = {
  production: false,
  firebaseConfig: {
    // Dev-specific config
    appId: "1:430652281252:web:4288b3b1a0194485fdf131",
    measurementId: "G-0M6FQTJKD8"
  },
  debugMode: true,
  features: {
    analytics: false,
    performance: false,
    crashlytics: false
  }
};

// Production environment
export const environment: Environment = {
  production: true,
  firebaseConfig: {
    // Prod-specific config
    appId: "1:430652281252:web:ae38ecaa63a04faffdf131",
    measurementId: "G-KYC66T1VLS"
  },
  debugMode: false,
  features: {
    analytics: true,
    performance: true,
    crashlytics: true
  }
};
```

## Security & Secrets Management

### Firebase Security Rules
```javascript
// firestore.rules - Production security
rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Strict authentication requirements
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isApproved() {
      return isAuthenticated() && 
             request.auth.token.status == 'approved';
    }
    
    function isAdmin() {
      return isAuthenticated() && 
             request.auth.token.role == 'admin' && 
             request.auth.token.status == 'approved';
    }
    
    // Default deny all
    match /{document=**} {
      allow read, write: if false;
    }
    
    // Specific collection rules...
  }
}
```

### API Keys & Secrets
```bash
# Firebase configuration (public - safe to expose)
FIREBASE_API_KEY=AIzaSyB03hVum4c_Q8hkYNbnRFbRZH5ox2HEMmw
FIREBASE_AUTH_DOMAIN=worthy-freshers.firebaseapp.com
FIREBASE_PROJECT_ID=worthy-freshers

# Sensitive configuration (server-side only)
FIREBASE_PRIVATE_KEY=<stored-in-firebase-functions-config>
FIREBASE_CLIENT_EMAIL=<stored-in-firebase-functions-config>

# Third-party integrations
SENDGRID_API_KEY=<stored-securely>
SLACK_WEBHOOK_URL=<stored-securely>
```

## Monitoring & Observability

### Firebase Analytics Setup
```typescript
// Analytics configuration
import { getAnalytics, logEvent } from '@angular/fire/analytics';

// Initialize analytics
const analytics = getAnalytics();

// Custom events
logEvent(analytics, 'user_registration', {
  method: 'email',
  role: 'student'
});

logEvent(analytics, 'admin_action', {
  action: 'approve_registration',
  target_user: uid
});
```

### Performance Monitoring
```typescript
// Performance monitoring setup
import { getPerformance, trace } from '@angular/fire/performance';

const performance = getPerformance();

// Custom traces
const registrationTrace = trace(performance, 'user_registration');
registrationTrace.start();
// ... registration logic
registrationTrace.stop();
```

### Error Tracking
```typescript
// Global error handler
@Injectable()
export class GlobalErrorHandler implements ErrorHandler {
  handleError(error: any): void {
    console.error('Global error:', error);
    
    // Log to Firebase Analytics
    logEvent(analytics, 'exception', {
      description: error.message,
      fatal: false
    });
    
    // Send to external monitoring service
    this.sendToMonitoring(error);
  }
  
  private sendToMonitoring(error: any) {
    // Integration with monitoring service
    // (Sentry, LogRocket, etc.)
  }
}
```

## Backup & Recovery

### Database Backup Strategy
```bash
# Automated Firestore backups
gcloud firestore export gs://worthy-freshers-backups/$(date +%Y-%m-%d)

# Scheduled backups (Cloud Scheduler)
# Daily: 2 AM UTC
# Weekly: Sunday 1 AM UTC
# Monthly: 1st day 12 AM UTC
```

### Disaster Recovery Plan
```
Recovery Time Objectives (RTO):
├── Critical Systems: 1 hour
├── User Data: 4 hours
├── Analytics Data: 24 hours
└── Historical Data: 72 hours

Recovery Point Objectives (RPO):
├── User Data: 15 minutes
├── System Config: 1 hour
├── Analytics: 4 hours
└── Logs: 24 hours
```

## Performance Optimization

### Build Optimization
```typescript
// Angular build optimizations
"budgets": [
  {
    "type": "initial",
    "maximumWarning": "500kb",
    "maximumError": "1mb"
  },
  {
    "type": "anyComponentStyle",
    "maximumWarning": "2kb",
    "maximumError": "4kb"
  }
],
"optimization": {
  "scripts": true,
  "styles": true,
  "fonts": true
},
"outputHashing": "all",
"sourceMap": false,
"namedChunks": false,
"extractLicenses": true,
"vendorChunk": false,
"buildOptimizer": true
```

### CDN & Caching
```json
// Firebase Hosting caching headers
"headers": [
  {
    "source": "**/*.@(js|css)",
    "headers": [
      {
        "key": "Cache-Control",
        "value": "max-age=31536000, immutable"
      }
    ]
  },
  {
    "source": "**/*.@(png|jpg|jpeg|gif|svg|webp)",
    "headers": [
      {
        "key": "Cache-Control",
        "value": "max-age=31536000, immutable"
      }
    ]
  },
  {
    "source": "/index.html",
    "headers": [
      {
        "key": "Cache-Control",
        "value": "no-cache, no-store, must-revalidate"
      }
    ]
  }
]
```

## Rollback Strategy

### Automated Rollback
```bash
# Firebase Hosting rollback
firebase hosting:clone SOURCE_SITE_ID:SOURCE_VERSION TARGET_SITE_ID

# Functions rollback
firebase functions:delete FUNCTION_NAME
firebase deploy --only functions:FUNCTION_NAME

# Database rollback (restore from backup)
gcloud firestore import gs://worthy-freshers-backups/BACKUP_DATE
```

### Manual Rollback Procedures
```
1. Identify Issue
   ├── Monitor alerts
   ├── User reports
   └── Performance metrics

2. Assess Impact
   ├── Affected users
   ├── System components
   └── Data integrity

3. Execute Rollback
   ├── Frontend (Firebase Hosting)
   ├── Backend (Cloud Functions)
   └── Database (if needed)

4. Verify Recovery
   ├── System functionality
   ├── User access
   └── Data consistency

5. Post-Incident
   ├── Root cause analysis
   ├── Documentation update
   └── Prevention measures
```

## Maintenance & Updates

### Regular Maintenance Tasks
```bash
# Weekly tasks
- Update dependencies
- Review security alerts
- Check performance metrics
- Backup verification

# Monthly tasks
- Security audit
- Performance optimization
- Cost analysis
- Documentation updates

# Quarterly tasks
- Disaster recovery testing
- Security penetration testing
- Architecture review
- Capacity planning
```

### Update Strategy
```
Dependency Updates:
├── Security patches: Immediate
├── Minor updates: Weekly
├── Major updates: Monthly (after testing)
└── Framework updates: Quarterly

Testing Strategy:
├── Unit tests: Every commit
├── Integration tests: Every merge
├── E2E tests: Every deployment
└── Performance tests: Weekly
```

---

*This document provides comprehensive details about deployment, DevOps practices, and operational procedures for the Worthy Freshers platform.*
