# Dashboard System Architecture

## Overview
The Worthy Freshers platform features a comprehensive dashboard system with role-based interfaces, real-time data updates, and responsive design. Each user role has a customized dashboard experience tailored to their specific needs and permissions.

## Dashboard Architecture

### System Structure
```
Dashboard Router (/dashboard)
├── Role Detection Logic
├── Dynamic Component Loading
└── Role-specific Dashboard Components
    ├── AdminDashboard
    ├── StudentDashboard
    ├── TrainerDashboard
    ├── ParentDashboard
    └── EmployeeDashboard (College Staff)
```

### Component Hierarchy
```
MainLayoutComponent
├── Header (Navigation, User Menu, Theme Switcher)
├── Sidenav (Role-based Navigation Menu)
├── Main Content Area
│   └── Dashboard Component (Role-specific)
├── Footer
└── Toast Notifications
```

## Role-based Dashboard System

### Dashboard Router Logic
```typescript
@Component({
  selector: 'app-dashboard',
  template: `
    <div class="dashboard-container">
      @if (userRole() === 'admin') {
        <app-admin-dashboard />
      } @else if (userRole() === 'student') {
        <app-student-dashboard />
      } @else if (userRole() === 'trainer') {
        <app-trainers-dashboard />
      } @else if (userRole() === 'parent') {
        <app-parent-dashboard />
      } @else if (userRole() === 'college-staff') {
        <app-employee-dashboard />
      } @else {
        <div class="error-state">
          <h2>Access Denied</h2>
          <p>Your role is not recognized or approved.</p>
        </div>
      }
    </div>
  `
})
export class Dashboard implements OnInit {
  userRole = signal<string | null>(null);
  
  async ngOnInit() {
    const claims = await this.getCurrentUserClaims();
    this.userRole.set(claims?.role || null);
  }
}
```

## Individual Dashboard Components

### 1. Admin Dashboard
**Purpose**: System administration, user management, and oversight

**Key Features**:
- Pending registration approvals (real-time)
- User management and role assignment
- System analytics and metrics
- Admin user creation
- Audit log monitoring

**Data Sources**:
- `pendingRegistrations` collection (real-time listener)
- User statistics aggregation
- System configuration data
- Audit logs

**UI Components**:
```typescript
interface AdminDashboardState {
  pendingRegistrations$: Observable<PendingRegistration[]>;
  isLoading: Signal<boolean>;
  isRefreshing: Signal<boolean>;
  showRejectModal: Signal<boolean>;
  selectedRegistration: Signal<PendingRegistration | null>;
  showCreateAdminForm: Signal<boolean>;
  totalApprovedUsers: Signal<number>;
  approvedToday: Signal<number>;
  rejectedToday: Signal<number>;
}
```

**Real-time Updates**:
```typescript
// Real-time pending registrations listener
this.pendingRegistrations$ = this.pendingRegistrationService
  .getPendingRegistrations()
  .pipe(
    tap(registrations => {
      console.log('Pending registrations updated:', registrations.length);
      this.isLoading.set(false);
    })
  );
```

### 2. Student Dashboard
**Purpose**: Course access, progress tracking, and academic management

**Key Features**:
- Course enrollment status
- Progress tracking
- Assignment submissions
- Grade viewing
- Schedule management
- Resource access

**Data Sources**:
- Student profile data
- Enrolled courses
- Progress metrics
- Upcoming assignments
- Announcements

### 3. Trainer Dashboard
**Purpose**: Course management, student oversight, and content delivery

**Key Features**:
- Course creation and management
- Student progress monitoring
- Assignment grading
- Resource sharing
- Schedule management
- Performance analytics

**Data Sources**:
- Assigned courses
- Student enrollments
- Performance metrics
- Content library
- Schedule data

### 4. Parent Dashboard
**Purpose**: Student progress monitoring and communication

**Key Features**:
- Child's academic progress
- Attendance tracking
- Grade monitoring
- Communication with trainers
- Event notifications
- Performance reports

**Data Sources**:
- Linked student data
- Progress reports
- Communication logs
- Event calendar
- Performance metrics

### 5. Employee Dashboard (College Staff)
**Purpose**: Institutional management and administrative tasks

**Key Features**:
- Institute management
- Student registration
- Trainer assignment
- Resource allocation
- Reporting and analytics
- Communication tools

**Data Sources**:
- Institute data
- Staff assignments
- Student records
- Resource inventory
- Performance reports

## Navigation System

### Sidenav Structure
```typescript
interface NavigationItem {
  label: string;
  route: string;
  icon: string;
  roles?: UserRole[];
  children?: NavigationItem[];
}

const navigationConfig: NavigationSection[] = [
  {
    title: 'MAIN',
    items: [
      {
        label: 'Dashboard',
        route: '/dashboard',
        icon: 'icon-[iconamoon--home-thin]'
      }
    ]
  },
  {
    title: 'REGISTRATIONS',
    items: [
      {
        label: 'Institute',
        route: '/registrations/institute',
        icon: 'icon-[iconamoon--building-thin]',
        roles: ['admin', 'college-staff']
      },
      {
        label: 'Student',
        route: '/registrations/student',
        icon: 'icon-[iconamoon--profile-thin]',
        roles: ['admin', 'college-staff', 'trainer']
      },
      // ... more registration items
    ]
  },
  {
    title: 'SYSTEM',
    items: [
      {
        label: 'Settings',
        route: '/system/settings',
        icon: 'icon-[iconamoon--settings-thin]',
        roles: ['admin']
      }
    ]
  }
];
```

### Role-based Navigation Filtering
```typescript
getFilteredNavigation(): NavigationSection[] {
  const userRole = this.getCurrentUserRole();
  
  return this.navigationConfig.map(section => ({
    ...section,
    items: section.items.filter(item => 
      !item.roles || item.roles.includes(userRole)
    )
  })).filter(section => section.items.length > 0);
}
```

## State Management

### Global State (Signals)
```typescript
// Global application state
export const toastConfig$ = signal<ToastConfig | null>(null);
export const selectedTheme$ = signal<string>(
  localStorage.getItem('theme') ?? 'autumn'
);

// Toast configuration interface
interface ToastConfig {
  status: 'success' | 'error' | 'warning' | 'info';
  message: string;
}
```

### Component-level State
```typescript
// Example: Admin Dashboard State
export class AdminDashboard {
  // Reactive signals for component state
  isLoading = signal(true);
  isRefreshing = signal(false);
  showRejectModal = signal(false);
  selectedRegistration = signal<PendingRegistration | null>(null);
  
  // Form state
  rejectionReason = '';
  createAdminForm = new FormGroup({
    name: new FormControl('', [Validators.required]),
    email: new FormControl('', [Validators.required, Validators.email]),
    password: new FormControl('', [Validators.required, Validators.minLength(6)])
  });
}
```

## Real-time Data Updates

### Firestore Real-time Listeners
```typescript
// Pending registrations real-time updates
private initializePendingRegistrationsListener(): void {
  this.auth.onAuthStateChanged(async (user) => {
    if (user) {
      const tokenResult = await user.getIdTokenResult();
      const isAdmin = tokenResult.claims?.['role'] === 'admin';

      if (isAdmin) {
        this.startPendingRegistrationsListener();
      }
    } else {
      this.pendingRegistrationsSubject.next([]);
    }
  });
}

private startPendingRegistrationsListener(): void {
  const pendingQuery = query(
    collection(this.firestore, 'pendingRegistrations'),
    where('status', '==', 'pending'),
    orderBy('submittedAt', 'desc')
  );

  onSnapshot(pendingQuery, (snapshot) => {
    const registrations = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as PendingRegistration[];
    
    this.pendingRegistrationsSubject.next(registrations);
  });
}
```

### Periodic Status Updates
```typescript
// Pending approval page auto-refresh
async ngOnInit() {
  await this.checkRegistrationStatus();

  // Set up periodic status checking (every 30 seconds)
  this.statusCheckInterval = setInterval(() => {
    this.refreshStatus();
  }, 30000);
}

ngOnDestroy() {
  if (this.statusCheckInterval) {
    clearInterval(this.statusCheckInterval);
  }
}
```

## UI/UX Design System

### Layout Components
```typescript
// Main Layout Structure
@Component({
  selector: 'app-main-layout',
  template: `
    <div class="min-h-screen bg-base-100">
      <app-header />
      <div class="flex">
        <app-sidenav />
        <main class="flex-1 p-6">
          <router-outlet />
        </main>
      </div>
      <app-footer />
      <app-toast />
    </div>
  `
})
export class MainLayoutComponent {}
```

### Theme System
```typescript
// Theme management with DaisyUI
const themes = [
  'light', 'dark', 'cupcake', 'bumblebee', 'emerald',
  'corporate', 'synthwave', 'retro', 'cyberpunk',
  'valentine', 'halloween', 'garden', 'forest',
  'aqua', 'lofi', 'pastel', 'fantasy', 'wireframe',
  'black', 'luxury', 'dracula', 'cmyk', 'autumn',
  'business', 'acid', 'lemonade', 'night', 'coffee', 'winter'
];

// Theme switcher component
@Component({
  selector: 'app-theme-switcher',
  template: `
    <div class="dropdown dropdown-end">
      <div tabindex="0" role="button" class="btn btn-ghost">
        <i class="icon-[iconamoon--palette-thin] text-xl"></i>
      </div>
      <ul class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow">
        @for (theme of themes; track theme) {
          <li>
            <button (click)="setTheme(theme)" 
                    [class.active]="selectedTheme$() === theme">
              {{ theme | titlecase }}
            </button>
          </li>
        }
      </ul>
    </div>
  `
})
export class ThemeSwitcher {
  themes = themes;
  selectedTheme$ = selectedTheme$;

  setTheme(theme: string) {
    selectedTheme$.set(theme);
    localStorage.setItem('theme', theme);
    document.documentElement.setAttribute('data-theme', theme);
  }
}
```

### Responsive Design
```css
/* Mobile-first responsive design */
.dashboard-container {
  @apply min-h-screen bg-base-100;
}

.dashboard-grid {
  @apply grid gap-6;
  @apply grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
}

.dashboard-card {
  @apply card bg-base-200 shadow-lg;
  @apply hover:shadow-xl transition-shadow duration-300;
}

.sidenav {
  @apply hidden lg:flex lg:w-64;
  @apply bg-base-200 min-h-screen;
}

@media (max-width: 1024px) {
  .sidenav {
    @apply fixed inset-y-0 left-0 z-50;
    @apply transform -translate-x-full transition-transform;
  }
  
  .sidenav.open {
    @apply translate-x-0;
  }
}
```

## Performance Optimizations

### Lazy Loading
```typescript
// Route-based lazy loading
const routes: Routes = [
  {
    path: 'dashboard',
    loadComponent: () => 
      import('./pages/dashboard/dashboard').then(m => m.Dashboard)
  },
  {
    path: 'registrations/student',
    loadComponent: () =>
      import('./pages/registrations/student-registration/student-registration')
        .then(m => m.StudentRegistration)
  }
];
```

### Change Detection Optimization
```typescript
// OnPush change detection strategy
@Component({
  selector: 'app-admin-dashboard',
  changeDetection: ChangeDetectionStrategy.OnPush,
  // ...
})
export class AdminDashboard {
  // Use signals for reactive state
  isLoading = signal(true);
  pendingCount = computed(() => this.pendingRegistrations().length);
}
```

### Memory Management
```typescript
// Proper cleanup in components
export class PendingApproval implements OnInit, OnDestroy {
  private statusCheckInterval?: number;

  ngOnInit() {
    this.statusCheckInterval = setInterval(() => {
      this.refreshStatus();
    }, 30000);
  }

  ngOnDestroy() {
    if (this.statusCheckInterval) {
      clearInterval(this.statusCheckInterval);
    }
  }
}
```

## Error Handling & User Feedback

### Toast Notification System
```typescript
// Global toast effect
@Injectable({ providedIn: 'root' })
export class Effects {
  toastConfigEffect = effect(() => {
    const config = toastConfig$();
    if (config) {
      console.log(config);
      setTimeout(() => {
        toastConfig$.set(null);
      }, 5000);
    }
  });
}

// Usage in components
showSuccessMessage(message: string) {
  toastConfig$.set({
    status: 'success',
    message: message
  });
}
```

### Loading States
```typescript
// Loading state management
async refreshPendingRegistrations() {
  if (this.isRefreshing()) return;

  try {
    this.isRefreshing.set(true);
    await new Promise(resolve => setTimeout(resolve, 500));
    
    toastConfig$.set({
      status: 'info',
      message: 'Pending registrations refreshed'
    });
  } catch (error) {
    toastConfig$.set({
      status: 'error',
      message: 'Failed to refresh data'
    });
  } finally {
    this.isRefreshing.set(false);
  }
}
```

## Accessibility & Usability

### ARIA Support
```html
<!-- Accessible navigation -->
<nav role="navigation" aria-label="Main navigation">
  <ul class="menu">
    <li>
      <a href="/dashboard" 
         aria-current="page" 
         [attr.aria-expanded]="isExpanded">
        Dashboard
      </a>
    </li>
  </ul>
</nav>

<!-- Accessible forms -->
<form [formGroup]="loginForm" (ngSubmit)="login()">
  <div class="form-control">
    <label class="label" for="email">
      <span class="label-text">Email</span>
    </label>
    <input id="email" 
           type="email" 
           class="input input-bordered"
           formControlName="email"
           aria-describedby="email-error"
           [attr.aria-invalid]="emailControl.invalid && emailControl.touched">
    <div id="email-error" class="label" *ngIf="emailControl.invalid && emailControl.touched">
      <span class="label-text-alt text-error">Email is required</span>
    </div>
  </div>
</form>
```

### Keyboard Navigation
```typescript
// Keyboard event handling
@HostListener('keydown', ['$event'])
onKeyDown(event: KeyboardEvent) {
  if (event.key === 'Escape') {
    this.closeModal();
  } else if (event.key === 'Enter' && event.ctrlKey) {
    this.submitForm();
  }
}
```

---

*This document provides comprehensive details about the dashboard system architecture and implementation in Worthy Freshers.*
