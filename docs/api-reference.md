# API Reference Guide

## Overview
The Worthy Freshers platform provides a comprehensive API through Firebase Cloud Functions, offering both callable functions (onCall) and HTTP endpoints. The API handles user registration, approval workflows, and administrative operations with robust security and error handling.

## API Architecture

### Function Types
```
Firebase Cloud Functions
├── onCall Functions (Firebase SDK)
│   ├── Automatic authentication
│   ├── Built-in CORS handling
│   └── Type-safe communication
└── HTTP Functions (REST API)
    ├── Manual authentication
    ├── Custom CORS handling
    └── Standard HTTP protocols
```

### Base URLs
```
Development:  https://us-central1-worthy-freshers.cloudfunctions.net/
QA:          https://us-central1-worthy-freshers.cloudfunctions.net/
Production:  https://us-central1-worthy-freshers.cloudfunctions.net/
```

## Authentication

### Firebase Authentication
All API endpoints require Firebase Authentication tokens:

```typescript
// Client-side authentication
import { Auth, getIdToken } from '@angular/fire/auth';

const token = await getIdToken(auth.currentUser);

// For HTTP endpoints
const headers = {
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json'
};

// For onCall functions (automatic)
const result = await httpsCallable(functions, 'functionName')(data);
```

### Custom Claims Structure
```typescript
interface UserClaims {
  role: 'admin' | 'student' | 'trainer' | 'parent' | 'college-staff';
  status: 'pending' | 'approved' | 'rejected';
  approvedAt?: number;
  approvedBy?: string;
  registeredAt: number;
}
```

## User Registration API

### Create Pending Registration

#### onCall Function
```typescript
// Function: createPendingRegistration
const createPendingRegistration = httpsCallable(functions, 'createPendingRegistration');

// Request
interface CreateRegistrationRequest {
  uid: string;
  email: string;
  name: string;
  role: 'student' | 'trainer' | 'parent' | 'college-staff';
  verificationData?: {
    studentId?: string;
    institutionEmail?: string;
    credentials?: string[];
    parentStudentLink?: string;
    requiresCredentials?: boolean;
    requiresInstitutionalEmail?: boolean;
    requiresStudentLinkage?: boolean;
    requiresExperienceVerification?: boolean;
    requiresDepartmentVerification?: boolean;
    requiresStudentId?: boolean;
  };
}

// Response
interface CreateRegistrationResponse {
  success: boolean;
  message: string;
  registrationId: string;
}

// Usage
try {
  const result = await createPendingRegistration({
    uid: 'user-uid',
    email: '<EMAIL>',
    name: 'John Doe',
    role: 'student',
    verificationData: {
      studentId: 'STU123456',
      requiresStudentId: true
    }
  });
  console.log(result.data);
} catch (error) {
  console.error('Registration failed:', error);
}
```

#### HTTP Endpoint
```typescript
// Endpoint: POST /createPendingRegistrationHttp
const response = await fetch(
  'https://us-central1-worthy-freshers.cloudfunctions.net/createPendingRegistrationHttp',
  {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      uid: 'user-uid',
      email: '<EMAIL>',
      name: 'John Doe',
      role: 'student',
      verificationData: {
        studentId: 'STU123456'
      }
    })
  }
);

const result = await response.json();
```

### Get Registration Status

#### onCall Function
```typescript
// Function: getRegistrationStatus
const getRegistrationStatus = httpsCallable(functions, 'getRegistrationStatus');

// Request (no parameters - uses authenticated user)
const result = await getRegistrationStatus();

// Response
interface RegistrationStatusResponse {
  status: 'pending' | 'approved' | 'rejected' | 'not_found';
  message?: string;
  registrationData?: {
    name: string;
    email: string;
    role: string;
    submittedAt: any;
    approvedAt?: any;
    rejectedAt?: any;
    rejectionReason?: string;
  };
  currentClaims?: any;
}
```

#### HTTP Endpoint
```typescript
// Endpoint: GET /getRegistrationStatusHttp
const response = await fetch(
  'https://us-central1-worthy-freshers.cloudfunctions.net/getRegistrationStatusHttp',
  {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  }
);

const status = await response.json();
```

## Admin Management API

### Approve Registration

#### onCall Function
```typescript
// Function: approvePendingRegistration (Admin only)
const approvePendingRegistration = httpsCallable(functions, 'approvePendingRegistration');

// Request
interface ApproveRegistrationRequest {
  registrationId: string; // User UID
}

// Response
interface ApproveRegistrationResponse {
  success: boolean;
  message: string;
  approvedUser: {
    uid: string;
    name: string;
    email: string;
    role: string;
  };
}

// Usage
try {
  const result = await approvePendingRegistration({
    registrationId: 'user-uid-to-approve'
  });
  console.log('User approved:', result.data.approvedUser);
} catch (error) {
  console.error('Approval failed:', error);
}
```

#### HTTP Endpoint
```typescript
// Endpoint: POST /approvePendingRegistrationHttp
const response = await fetch(
  'https://us-central1-worthy-freshers.cloudfunctions.net/approvePendingRegistrationHttp',
  {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${adminToken}`
    },
    body: JSON.stringify({
      registrationId: 'user-uid-to-approve'
    })
  }
);

const result = await response.json();
```

### Reject Registration

#### onCall Function
```typescript
// Function: rejectPendingRegistration (Admin only)
const rejectPendingRegistration = httpsCallable(functions, 'rejectPendingRegistration');

// Request
interface RejectRegistrationRequest {
  registrationId: string;
  rejectionReason: string;
}

// Response
interface RejectRegistrationResponse {
  success: boolean;
  message: string;
  rejectedUser: {
    uid: string;
    name: string;
    email: string;
    role: string;
  };
}

// Usage
try {
  const result = await rejectPendingRegistration({
    registrationId: 'user-uid-to-reject',
    rejectionReason: 'Invalid credentials provided'
  });
  console.log('User rejected:', result.data.rejectedUser);
} catch (error) {
  console.error('Rejection failed:', error);
}
```

### Create Admin User

#### onCall Function
```typescript
// Function: createAdminUser (Admin only)
const createAdminUser = httpsCallable(functions, 'createAdminUser');

// Request
interface CreateAdminRequest {
  name: string;
  email: string;
  password: string;
}

// Response
interface CreateAdminResponse {
  success: boolean;
  message: string;
  adminUser: {
    uid: string;
    name: string;
    email: string;
    role: 'admin';
  };
}

// Usage
try {
  const result = await createAdminUser({
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'securePassword123'
  });
  console.log('Admin created:', result.data.adminUser);
} catch (error) {
  console.error('Admin creation failed:', error);
}
```

## Utility Functions

### Hello World (Health Check)

#### onCall Function
```typescript
// Function: helloWorld
const helloWorld = httpsCallable(functions, 'helloWorld');

// Request (no parameters)
const result = await helloWorld({});

// Response
interface HelloWorldResponse {
  message: string; // "Hello from Firebase!"
}
```

### Legacy User Role Update

#### onCall Function (Deprecated)
```typescript
// Function: updateUserRole (Deprecated - use pending registration system)
const updateUserRole = httpsCallable(functions, 'updateUserRole');

// Request
interface UpdateRoleRequest {
  uid: string;
  role: string;
}

// Response
interface UpdateRoleResponse {
  message: string;
}

// Note: This function is deprecated and sets status to 'pending'
// Use the new pending registration system instead
```

## Error Handling

### Error Types
```typescript
// Firebase Functions errors
interface FirebaseFunctionError {
  code: string;
  message: string;
  details?: any;
}

// Common error codes
enum ErrorCodes {
  UNAUTHENTICATED = 'unauthenticated',
  PERMISSION_DENIED = 'permission-denied',
  INVALID_ARGUMENT = 'invalid-argument',
  NOT_FOUND = 'not-found',
  ALREADY_EXISTS = 'already-exists',
  INTERNAL = 'internal'
}
```

### Error Handling Examples
```typescript
// onCall function error handling
try {
  const result = await createPendingRegistration(data);
  return result.data;
} catch (error) {
  if (error.code === 'permission-denied') {
    console.error('Access denied:', error.message);
  } else if (error.code === 'invalid-argument') {
    console.error('Invalid data:', error.message);
  } else {
    console.error('Unexpected error:', error);
  }
  throw error;
}

// HTTP endpoint error handling
const response = await fetch(endpoint, options);

if (!response.ok) {
  const errorData = await response.json();
  throw new Error(`HTTP ${response.status}: ${errorData.message}`);
}

const result = await response.json();
```

## Rate Limiting & Quotas

### Firebase Functions Limits
```
Concurrent Executions: 1,000 (default)
Execution Time: 540 seconds (9 minutes)
Memory: 256MB - 8GB
Network Egress: 5GB/month (free tier)
Invocations: 2,000,000/month (free tier)
```

### Best Practices
```typescript
// Implement client-side rate limiting
class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  
  canMakeRequest(endpoint: string, maxRequests: number = 10, windowMs: number = 60000): boolean {
    const now = Date.now();
    const requests = this.requests.get(endpoint) || [];
    
    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < windowMs);
    
    if (validRequests.length >= maxRequests) {
      return false;
    }
    
    validRequests.push(now);
    this.requests.set(endpoint, validRequests);
    return true;
  }
}

// Usage
const rateLimiter = new RateLimiter();

if (rateLimiter.canMakeRequest('createPendingRegistration', 5, 60000)) {
  await createPendingRegistration(data);
} else {
  throw new Error('Rate limit exceeded. Please try again later.');
}
```

## Security Considerations

### Input Validation
```typescript
// Server-side validation example
function validateRegistrationData(data: any): void {
  if (!data.uid || typeof data.uid !== 'string') {
    throw new functions.https.HttpsError(
      'invalid-argument',
      'UID is required and must be a string'
    );
  }
  
  if (!data.email || !isValidEmail(data.email)) {
    throw new functions.https.HttpsError(
      'invalid-argument',
      'Valid email is required'
    );
  }
  
  if (!data.role || !['student', 'trainer', 'parent', 'college-staff'].includes(data.role)) {
    throw new functions.https.HttpsError(
      'invalid-argument',
      'Valid role is required'
    );
  }
}

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
```

### Authorization Checks
```typescript
// Admin authorization example
async function requireAdmin(context: functions.https.CallableContext): Promise<void> {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      'unauthenticated',
      'User must be authenticated'
    );
  }

  const adminToken = await admin.auth().getUser(context.auth.uid);
  const adminClaims = adminToken.customClaims;

  if (!adminClaims?.role || adminClaims.role !== 'admin') {
    throw new functions.https.HttpsError(
      'permission-denied',
      'Admin privileges required'
    );
  }

  if (adminClaims.status !== 'approved') {
    throw new functions.https.HttpsError(
      'permission-denied',
      'Admin account must be approved'
    );
  }
}
```

## Testing

### Unit Testing Functions
```typescript
// Jest test example
import { createPendingRegistration } from '../src/lib/pending-registration';

describe('createPendingRegistration', () => {
  it('should create a pending registration', async () => {
    const mockRequest = {
      data: {
        uid: 'test-uid',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'student'
      }
    };

    const mockContext = {
      auth: { uid: 'test-uid' }
    };

    const result = await createPendingRegistration(mockRequest, mockContext);
    
    expect(result.success).toBe(true);
    expect(result.registrationId).toBe('test-uid');
  });
});
```

### Integration Testing
```typescript
// Firebase Functions testing
import { initializeTestApp, clearFirestoreData } from '@firebase/rules-unit-testing';

describe('Registration API Integration', () => {
  let app: any;

  beforeEach(async () => {
    app = initializeTestApp({
      projectId: 'test-project',
      auth: { uid: 'test-user', role: 'admin' }
    });
  });

  afterEach(async () => {
    await clearFirestoreData({ projectId: 'test-project' });
  });

  it('should handle complete registration flow', async () => {
    // Test registration creation
    // Test admin approval
    // Test user access grant
  });
});
```

---

*This document provides comprehensive API reference and usage examples for the Worthy Freshers platform.*
