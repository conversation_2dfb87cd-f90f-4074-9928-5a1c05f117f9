/**
 * <PERSON><PERSON>p script to create the first admin user
 * Run this once to create your initial admin user
 *
 * Usage:
 * 1. Update the admin details below
 * 2. Run: node create-admin.js
 * 3. Delete this file after use for security
 */

const admin = require('firebase-admin');

// Initialize Firebase Admin (make sure you have your service account key)
// You can download it from Firebase Console > Project Settings > Service Accounts
const serviceAccount = require('path/to/your/serviceAccountKey.json'); // Update this path

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  // Add your database URL if needed
  // databaseURL: "https://your-project-id-default-rtdb.firebaseio.com"
});

const db = admin.firestore();

// Admin user details - UPDATE THESE
const ADMIN_USER = {
  email: 'REPLACE_THIS', // Change this
  password: 'REPLACE_THIS', // Change this
  name: 'REPLACE_THIS', // Change this
};

async function createAdmin() {
  try {
    console.log('Creating first admin user...');

    // Create the user in Firebase Auth
    const userRecord = await admin.auth().createUser({
      email: ADMIN_USER.email,
      password: ADMIN_USER.password,
      displayName: ADMIN_USER.name,
      emailVerified: true,
    });

    console.log(`✅ Firebase Auth user created: ${userRecord.uid}`);

    // Set admin custom claims
    await admin.auth().setCustomUserClaims(userRecord.uid, {
      role: 'admin',
      status: 'approved',
      approvedAt: Date.now(),
      createdByBootstrap: true,
    });

    console.log('✅ Admin custom claims set');

    // Create a record in pendingRegistrations for tracking
    const adminRegistration = {
      uid: userRecord.uid,
      email: ADMIN_USER.email,
      name: ADMIN_USER.name,
      role: 'admin',
      status: 'approved',
      submittedAt: admin.firestore.Timestamp.now(),
      approvedAt: admin.firestore.Timestamp.now(),
      verificationData: {
        createdByBootstrap: true,
        createdAt: new Date().toISOString(),
      },
    };

    await db
      .collection('pendingRegistrations')
      .doc(userRecord.uid)
      .set(adminRegistration);

    console.log('✅ Admin registration record created');

    console.log('\n🎉 First admin user created successfully!');
    console.log(`📧 Email: ${ADMIN_USER.email}`);
    console.log(`🔑 Password: ${ADMIN_USER.password}`);
    console.log(`👤 Name: ${ADMIN_USER.name}`);
    console.log(`🆔 UID: ${userRecord.uid}`);

    console.log('\n⚠️  IMPORTANT:');
    console.log('1. Save these credentials securely');
    console.log('2. Delete this script file for security');
    console.log('3. Change the password after first login');
    console.log(
      '4. You can now create additional admins from the admin dashboard'
    );

    process.exit(0);
  } catch (error) {
    console.error('❌ Error creating first admin user:', error);

    if (error.code === 'auth/email-already-exists') {
      console.log('\n💡 The email already exists. You might want to:');
      console.log('1. Use a different email');
      console.log('2. Or manually set admin claims for the existing user');
    }

    process.exit(1);
  }
}

// Run the script
createAdmin().catch(console.error);
